import { defineConfig, loadEnv } from '@echronos/ech-micro';
import path from 'path';
const __dirname = path.resolve();

const envConfig = loadEnv(process.env.NODE_ENV, './', 'BIZ_');

export default defineConfig({
  name: 'build',
  base: envConfig.BIZ_BUIDE_SITE_APP_DOMAIN || '/',
  compiler: 'swc',
  webpack: {
    snapshot: {
      managedPaths: [path.resolve(__dirname, './node_modules/@echronos/millet-ui')],
    },
  },
  webpackChain: (chain) => {
    chain.resolve.alias.set('@', path.resolve(__dirname, 'src'));
    return chain;
  },
});

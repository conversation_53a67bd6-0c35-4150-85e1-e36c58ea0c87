// import { waitTime } from '@/utils/tools';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { usePageStore } from './use-page';

export interface SiderState {
  collapsed: boolean;
  isBreak: boolean;
  status: 'none' | 'leave' | 'enter';
  levitate: boolean;
}

type SiderStateAction = {
  setSiderLevitateShow: () => void;
  setSiderLevitateHide: () => void;
  setSiderToggle: (collapsed: SiderState['collapsed']) => void;
  setSiderState: (state: Partial<SiderState>) => void;
  resetPageData: () => void;
};

export const useSiderStore = create<SiderState & SiderStateAction>()(
  persist(
    (set, get) => ({
      collapsed: false,
      isBreak: false,
      levitate: false,
      status: 'none',
      setSiderLevitateShow: async () => {
        // const { levitate } = get();
        // if (!levitate) {
        //   await waitTime(100);
        // }
        const { collapsed } = get();
        if (collapsed) {
          return set(() => ({ levitate: true, isBreak: true }));
        }
      },
      setSiderLevitateHide: async () => {
        // await waitTime(500);
        const { isBreak, collapsed, resetPageData } = get();
        if (collapsed) {
          if (isBreak) {
            return set(() => ({ levitate: false, isBreak: false }));
          }
          resetPageData();
          return set(() => ({ levitate: false, isBreak: false }));
        }
      },
      setSiderState: (state) => set(() => ({ ...state })),
      setSiderToggle: (collapsed: SiderState['collapsed']) => {
        const { resetPageData } = get();
        resetPageData();
        return set(() => ({ collapsed, levitate: false, status: 'none', renameVisible: false }));
      },
      resetPageData: () => {
        const { setPageState } = usePageStore.getState();
        setPageState({ renameVisible: false });
      },
    }),
    {
      name: 'sider-storage',
      // whitelist: ['name', 'age'],
    }
  )
);

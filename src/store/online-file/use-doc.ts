import { create } from 'zustand';
import { IndexeddbPersistence } from 'y-indexeddb';
import WebsocketProvider from '@echronos/editor/dist/pkg-y-websocket';

export interface User {
  id: number;
  nickname: string;
  avatar: string;
  active: boolean;
}

export interface DocState {
  docLoading: boolean;
  onlineUsers: User[];
  socketProvider: WebsocketProvider | null;
  indexDBProvider: IndexeddbPersistence | null; // IndexeddbPersistence
  setDocLoading: (bol: boolean) => void;
  setOnlineUsers: (user: User[]) => void;
  setIndexDBProvider: (ins: IndexeddbPersistence | null) => void;
  setSocketProvider: (ins: WebsocketProvider | null) => void;
}

const useDocState = create<DocState>((set) => ({
  docLoading: true,
  onlineUsers: [],
  indexDBProvider: null,
  socketProvider: null,
  setDocLoading(bol) {
    set(() => ({ docLoading: bol }));
  },
  setOnlineUsers(users) {
    set(() => ({ onlineUsers: users }));
  },
  setIndexDBProvider(ins) {
    set(() => ({ indexDBProvider: ins }));
  },
  setSocketProvider(ins) {
    set(() => ({ socketProvider: ins }));
  },
}));

export default useDocState;

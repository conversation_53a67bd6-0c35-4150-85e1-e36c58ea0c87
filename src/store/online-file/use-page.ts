import type { DataNode } from 'antd/es/tree';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type PageTreeDataNode = {
  title: string;
  id: string;
  isIndex?: boolean;
  children?: PageTreeDataNode[];
} & DataNode;

const treeData = [
  {
    title: '作品展示 111',
    key: '0-0',
    id: '0-0',
    icon: '',
    isIndex: true,
    children: [
      {
        title: 'leaf',
        key: '0-0-0',
        id: '0-0-0',
        icon: '',
        children: [
          {
            title: '666leaf',
            key: '0-0-1',
            id: '0-0-1',
            icon: '',
          },
        ],
      },
    ],
  },
  {
    title: '华世界产品介绍123',
    key: '0-2',
    id: '0-2',
    icon: '',
    children: [
      {
        title: 'leaf',
        key: '0-2-1',
        id: '0-2-1',
        icon: '',
      },
      {
        title: 'leaf',
        key: '0-2-2',
        id: '0-2-2',
        icon: '',
      },
    ],
  },
];
type ongoingNodeDataType = Pick<PageTreeDataNode, 'title' | 'id'> &
  Partial<Omit<PageTreeDataNode, 'title' | 'id'>>;
export interface PageState {
  currentPageId: string;
  renameVisible: boolean;
  ongoingNodeData: ongoingNodeDataType | PageTreeDataNode;
  pageTreeData: PageTreeDataNode[];
}

type PageStateAction = {
  // eslint-disable-next-line no-unused-vars
  setPageState: (state: Partial<PageState>) => void;
  // eslint-disable-next-line no-unused-vars
  changeNodeData: (data: Partial<PageTreeDataNode>) => void;
  updateTreeNodeData: () => void;
};

function treeFindUpdate(tree: PageTreeDataNode[], data: ongoingNodeDataType): PageTreeDataNode[] {
  for (let i = 0; i < tree.length; i += 1) {
    const item = tree[i];
    if (item.id === data.id) {
      item.title = data.title;
      item.icon = data.icon;
      item.isIndex = data.isIndex;
      break;
    }
    if (item.children) {
      // 只对非末端节点进行递归
      treeFindUpdate(item.children, data);
    }
  }
  return tree;
}

export const usePageStore = create<PageState & PageStateAction>()(
  persist(
    (set, get) => ({
      currentPageId: '',
      renameVisible: false,
      ongoingNodeData: { title: '', id: '' },
      pageTreeData: treeData,
      changeNodeData: (data) =>
        set((state) => ({ ongoingNodeData: { ...state.ongoingNodeData, ...data } })),
      updateTreeNodeData: () => {
        const { ongoingNodeData, pageTreeData } = get();
        const nextTree = treeFindUpdate(pageTreeData, ongoingNodeData);
        return set(() => ({ pageTreeData: nextTree }));
      },
      setPageState: (state) => set(() => ({ ...state })),
    }),
    {
      name: 'page-storage',
      // whitelist: ['name', 'age'],
    }
  )
);

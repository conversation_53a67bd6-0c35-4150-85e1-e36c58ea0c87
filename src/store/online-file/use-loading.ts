import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Loading {
  loading: boolean;
}

interface LoadingActions {
  setLoading: (bol: boolean) => void;
}

const useLoading = create<Loading & LoadingActions>()(
  persist(
    (set) => {
      return {
        loading: false,
        setLoading(bol) {
          set(() => ({ loading: bol }));
        },
      };
    },
    { name: 'loading-storage' }
  )
);

export default useLoading;

import { makeAutoObservable } from 'mobx';
import type { DocumentHeadCoverData } from '@echronos/millet-ui';
// import autoUpdateTitle from '@/utils/auto-update-title';

// 文档编辑头部数据
class Head {
  headData: DocumentHeadCoverData = {
    title: '',
    avatar: {
      type: 'icon',
      url: '',
    },
    background: {
      url: '',
      transform: 40,
    },
  };

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  setHeadData(val: DocumentHeadCoverData) {
    this.headData = val;
    // autoUpdateTitle(val.title);
  }
}

export default new Head();

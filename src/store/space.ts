import { action, makeObservable, observable } from 'mobx';
import type { SpaceListType } from '@/apis/site-manager/get-space-list';

export interface SpaceInfoData {
  name: string;
  logo: string;
  description: string;
  blockId: string;
  spaceId: string;
  content: string[];
  ownerId: number;
}

export interface PageInfoData {
  perCodes: string[];
  attrs: {
    pageName: string;
  };
  type: string;
  blockId: string;
  spaceId: string;
}

class Space {
  // 空间信息
  spaceInfo: SpaceInfoData = {} as SpaceInfoData;

  // 页面信息
  pageInfo: PageInfoData = {} as PageInfoData;

  // 申请人数
  spaceApplyNum = 0;

  // 头部面包屑
  headerNavList: SpaceListType[] = [] as SpaceListType[];

  // 团队空间
  spaceGroupList: SpaceListType[] = [] as SpaceListType[];

  // 分享的页面
  spaceShareList: SpaceListType[] = [] as SpaceListType[];

  constructor() {
    makeObservable(this, {
      spaceInfo: observable,
      pageInfo: observable,
      spaceApplyNum: observable,
      headerNavList: observable,
      spaceGroupList: observable,
      spaceShareList: observable,

      setSpaceInfo: action,
      setSpaceApplyNum: action,
      setHeaderNavList: action,
      setSpaceGroupList: action,
      setSpaceShareList: action,
    });
  }

  setSpaceInfo(val: SpaceInfoData) {
    this.spaceInfo = val;
  }

  setPageInfo(val: PageInfoData) {
    this.pageInfo = val;
  }

  setSpaceApplyNum(val: number) {
    this.spaceApplyNum = val;
  }

  setHeaderNavList(val: SpaceListType[]) {
    this.headerNavList = val;
  }

  setSpaceGroupList(val: SpaceListType[]) {
    this.spaceGroupList = val;
  }

  setSpaceShareList(val: SpaceListType[]) {
    this.spaceShareList = val;
  }

  initStorage() {
    const groupData = localStorage.getItem('GROUP_LIST');
    const shareData = localStorage.getItem('SHARE_LIST');
    if (groupData && JSON.parse(groupData).length) {
      this.spaceGroupList = JSON.parse(groupData);
    }
    if (shareData && JSON.parse(shareData).length) {
      this.spaceShareList = JSON.parse(shareData);
    }
  }
}

const space = new Space();

// space.initStorage();

export default space;

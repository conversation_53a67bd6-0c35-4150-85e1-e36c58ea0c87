/**
 * 返回上一页 hooks
 */
import { useCallback } from 'react';
import { Location, To, NavigateFunction, useLocation, useNavigate } from 'react-router-dom';
import isNumber from 'lodash/isNumber';
import isString from 'lodash/isString';
import getEnv from '@/utils/env';

/**
 * 获取返回函数 hooks
 * @param delta {string|number} 默认返回步数
 * @param defaultNavUrl {string} 无法返回指定页数时重定向的地址
 */
function useBack(
  delta: number | string = -1,
  defaultNavUrl = getEnv('BIZ_APP_HOME_URL') || '/'
): [
  back: MultipleParamsFn<[defaultUrl?: unknown]>,
  pathBack: MultipleParamsFn<[path: To]>,
  navigate: NavigateFunction,
  location: Location
] {
  const location = useLocation();
  const navigate = useNavigate();
  const back = useCallback(
    (opt?: unknown) => {
      if (typeof window !== 'undefined' && location.key === window.START_LOCATION_KEY) {
        navigate((isString(opt) && opt) || (isString(delta) ? delta : defaultNavUrl), {
          replace: true,
        });
      } else {
        const backDelta = isNumber(opt) ? opt : delta;
        navigate(isNumber(backDelta) ? backDelta : -1);
      }
    },
    [defaultNavUrl, delta, location.key, navigate]
  );
  const pathBack = useCallback(
    (path: To) => {
      navigate(path || defaultNavUrl, { replace: true });
    },
    [defaultNavUrl, navigate]
  );

  return [back, pathBack, navigate, location];
}

export default useBack;

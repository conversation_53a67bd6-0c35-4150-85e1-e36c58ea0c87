/* eslint-disable no-underscore-dangle */
// eslint-disable-next-line import/no-extraneous-dependencies
import '@echronos/ech-micro/runtime';
import { CLIENT, onReady } from '@echronos/core';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN'; // antd中文
import msMY from 'antd/es/locale/ms_MY'; // antd马来语
import enGB from 'antd/es/locale/en_GB'; // antd英语
import { Locale } from 'antd/lib/locale-provider';
import ReactDOM from 'react-dom';
import { BrowserRouter } from 'react-router-dom';
import '@echronos/millet-ui/dist/index.css';
// @ts-ignore
import { ComponentContext } from '@echronos/echos-ui';
import App from './app';
import './index.less';
import './i18n';
// millet-ui使用antd中的组件,build-site中没有使用该组件，会丢失样式
import '@echronos/antd/lib/pagination/style/index.css';

interface Languages {
  [key: string]: Locale;
  zh: Locale;
  ms: Locale;
  en: Locale;
}

// 👇 将渲染操作放入 mount 函数 -- 必填
export function mount() {
  const lang = localStorage.getItem('lang') || 'zh'; // 系统多语言配置
  const languages: Languages = {
    zh: zhCN,
    ms: msMY,
    en: enGB,
  }; // antd多语言切换

  onReady(() => {
    ReactDOM.render(
      <BrowserRouter basename={(CLIENT && window.__MICRO_APP_BASE_ROUTE__) || '/'}>
        <ConfigProvider locale={languages[lang]}>
          <ComponentContext.Provider value={{ lang }}>
            {/* message 根节点不能挂载在micro-root上， ts会报不能重复render的错 */}
            <div id="root-app">
              <App />
            </div>
          </ComponentContext.Provider>
        </ConfigProvider>
      </BrowserRouter>,
      document.querySelector('#micro-root')!
    );
  });
}

// 👇 将卸载操作放入 unmount 函数 -- 必填
export function unmount() {
  ReactDOM.unmountComponentAtNode(document.querySelector('#micro-root')!);
}

// 微前端环境下，注册mount和unmount方法
if (CLIENT && window.__MICRO_APP_ENVIRONMENT__) {
  // @ts-ignore
  window[`micro-app-${window.__MICRO_APP_NAME__}`] = { mount, unmount };
} else {
  // 非微前端环境直接渲染
  mount();
}

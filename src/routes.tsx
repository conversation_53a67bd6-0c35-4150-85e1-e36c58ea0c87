import { RouteObject } from 'react-router-dom';
import { lazy } from 'react';
import OnlineFile from './pages/online-file/online-file';
import Workspaces from './pages/online-file/workspaces';
import Settings from './pages/online-file/settings';
import OperationCenter from './pages/online-file/operation-center';
import TemplateOperation from './pages/online-file/operation-center/template-operation';
import ClassMange from './pages/online-file/operation-center/class-mange';
import TopicManagement from './pages/online-file/operation-center/topic-management';
import TemplateSetting from './pages/online-file/template-settings';
import TemplateMarket from './pages/online-file/template-market';
import EnterpriseTemplate from './pages/online-file/enterprise-template';
import SpaceSetup from './pages/online-file/space-setup';
import ManpowerMarket from './pages/online-file/operation-center/manpower-market';
import AgentClassMange from './pages/online-file/operation-center/agent-class-mange';
import LanguageSettings from './pages/online-file/settings/language-settings';

const SeoSettings = lazy(() => import('./pages/online-file/settings/seo-settings'));

const config: RouteObject[] = [
  {
    path: '/file',
    element: <OnlineFile />,
    children: [
      {
        path: 'settings',
        element: <Settings />,
        children: [
          {
            path: 'seo',
            element: <SeoSettings />,
          },
          {
            path: 'language',
            element: <LanguageSettings />,
          },
        ],
      },
      {
        path: ':blockId',
        element: <Workspaces />,
        index: true,
      },
      {
        path: 'template-setting',
        element: <TemplateSetting />,
      },
      {
        path: 'template-market',
        element: <TemplateMarket />,
      },
      {
        path: 'enterprise-template',
        element: <EnterpriseTemplate />,
      },
      {
        path: 'space-setup',
        element: <SpaceSetup />,
      },
    ],
  },
  {
    path: '/operation-center',
    element: <OperationCenter />,
    children: [
      {
        path: 'template-operation',
        element: <TemplateOperation />,
      },
      {
        path: 'class-manage',
        element: <ClassMange />,
      },
      {
        path: 'topic-management',
        element: <TopicManagement />,
      },
      {
        path: 'manpower-market',
        element: <ManpowerMarket />,
      },
      {
        path: 'agent-class-mange',
        element: <AgentClassMange />,
      },
    ],
  },
];

export default config;

// eslint-disable-next-line import/no-extraneous-dependencies
import { Wrapper } from '@echronos/react';
import { onReady, userInit } from '@echronos/core';
import { LoadingOutlined } from '@echronos/icons';
import { useEffect, useState } from 'react';
import { useRoutes } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import routes from './routes';
// import './styles/app.css';

function App() {
  const { t } = useTranslation();
  const router = useRoutes(routes);
  const [value, setValue] = useState(null as any);
  const [isReady, setReady] = useState(false);

  useEffect(() => {
    onReady(() => {
      userInit().then((data) => {
        setValue(data as any);

        setReady(true);
      });
    });
  }, []); // eslint-disable-line

  return (
    <Wrapper value={value}>
      <div className="micro-app__root">
        {isReady ? (
          router
        ) : (
          <div className="load-page">
            <LoadingOutlined />
            <p className="mt-2 load-page-text">{t('build_enter_loading')}</p>
          </div>
        )}
      </div>
    </Wrapper>
  );
}

export default App;

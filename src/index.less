@import '@echronos/antd/lib/style/index.less';
@import '@echronos/react/less/reboot';
@import '@/styles/mantle';

html {
  font-size: @font-size-base;
}

html,
body {
  width: 100vw;
  height: 100vh;
}

body {
  color: @text-color-secondary;
  font-size: 14px;
  margin: 0;
}

p {
  word-break: break-all;
}

input {
  padding: 0;
}

#micro-root {
  width: 100%;
  height: 100%;
}

.load-page {
  color: var(--workspace-primary-color);
  font-size: @font-size-lg;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &-text {
    color: @text-color-secondary;
    font-size: @font-size-xs;
  }
}

.micro-app {
  &__root {
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 18px;
    // padding: 0 16px 16px;
    height: 100%;
  }
}

#root-app {
  height: 100%;
}

::selection {
  background-color: #ece8ff;
}

::selection {
  color: inherit;
}

.ant-message {
  z-index: 99999 !important;
}

.anticon svg {
  width: 1em;
  height: 1em;
  fill: currentcolor;
}

.eos-layout {
  width: 100%;
}

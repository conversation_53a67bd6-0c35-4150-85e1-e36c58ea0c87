import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import zh from './locales/zh.json';
import ms from './locales/ms.json';
import en from './locales/en.json';

const resources = {
  zh: {
    translation: zh,
  },
  ms: {
    translation: ms,
  },
  en: {
    translation: en,
  },
};

const lang = localStorage.getItem('lang') || 'zh';

i18n.use(initReactI18next).init({
  resources,
  fallbackLng: lang,
  interpolation: {
    escapeValue: false,
  },
  detection: {
    order: ['path', 'htmlTag', 'cookie', 'navigator'],
    caches: ['localStorage'],
  },
});

export default i18n;

.container {
  display: flex;
  height: 362px;
  justify-content: space-between;
}

.cropArea {
  width: 432px;
  height: 100%;
  // display: flex;
  // flex-direction: column;
  // justify-content: center;
  // background-color: rgba(0, 0, 0, 0.6);
}

.handelArea {
  flex: 1;
  padding-left: 20px;

  .title {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 12px;
  }
}

.aspectBox {
  padding-top: 16px;

  .aspectNumberBox {
    width: 57px;
  }

  .spliteMark {
    margin: 0 8px;
  }
}

.linkBtn {
  width: fit-content !important;
  min-width: 0 !important;
  padding: 0 !important;
}

.fotter {
  display: flex;
  padding: 20px 0;
  justify-content: space-between;

  .save {
    margin-left: 16px;
  }
}

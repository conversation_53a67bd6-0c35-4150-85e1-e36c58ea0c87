import { Slider, InputNumber } from 'antd';
import { useState } from 'react';
import styles from './slider.module.less';

// eslint-disable-next-line no-unused-vars
function SliderIpt({
  value,
  min,
  max,
  onChange,
}: {
  value: number;
  min?: number;
  max?: number;
  // eslint-disable-next-line no-unused-vars
  onChange: (val: number) => void;
}) {
  const [val, setVal] = useState(value);

  const onValChange = (v: number | null) => {
    setVal(v || 0);
    if (onChange) {
      onChange(v || 0);
    }
  };
  return (
    <div className={styles.sliderBox}>
      <Slider value={val} min={min} max={max} className={styles.slider} onChange={onValChange} />
      <div className={styles.iptBox}>
        <InputNumber
          className={styles.ipt}
          min={min}
          max={max}
          defaultValue={val}
          value={val}
          onChange={onValChange}
        />
      </div>
    </div>
  );
}

SliderIpt.defaultProps = {
  min: 1,
  max: 100,
};

export default SliderIpt;

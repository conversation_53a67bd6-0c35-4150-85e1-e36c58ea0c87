.sliderBox {
  display: flex;
  padding: 0 6px;
  justify-content: space-between;

  :global {
    .ant-slider-track {
      // background-color: #008cff;
      background: linear-gradient(106deg, #441eff 0%, #823eff 100%);
    }

    .ant-slider-handle {
      // background-color: #008cff;
      background: linear-gradient(135deg, #441eff 0%, #823eff 100%);
      border: solid 2px #fff;
      box-shadow: 0 4px 10px 0 rgb(0 0 0 / 30%);
      width: 16px;
      height: 16px;
      margin-top: -6px;
    }

    .ant-input-number-handler-wrap {
      display: none;
    }

    .ant-input-number-input {
      height: 100%;
      border-radius: 4px;
    }

    .ant-input-number-input-wrap {
      height: 100%;
    }
  }
}

.slider {
  flex: 1;
}

.iptBox {
  width: 46px;
  text-align: right;
  line-height: 32px;

  .ipt {
    width: 42px;
    height: 26px;
  }
}

import type ObsClient from 'esdk-obs-browserjs';
import i18n from 'i18next';
import { getObsToken, ObsTokenResult } from '@/apis';
import getEnv from '@/utils/env';
import isString from 'lodash/isString';
import { isImageUrl } from './util';

interface LoadingQueueResult {
  config: ObsTokenResult;
  instance: ObsClient;
}

let opt: ObsTokenResult | null = null;
let obsClient: ObsClient | null = null;
let loadingQueue: any = null;

function appendScript<Value = any>(
  varName: string,
  script: string,
  onSuccess?: any,
  onError?: any
) {
  if (getEnv('SSR')) {
    return;
  }
  // @ts-ignore
  const plugin = window[varName] as Value | null;
  if (plugin) {
    onSuccess?.(plugin);
  } else {
    const scriptEl = document.createElement('script');
    scriptEl.src = script;
    scriptEl.async = true;
    scriptEl.defer = true;

    const handleError: any = (e) => {
      onError?.(e);
      scriptEl.remove?.();
    };
    scriptEl.addEventListener(
      'load',
      () => {
        // @ts-ignore
        onSuccess?.(window[varName] as Value);
        scriptEl.removeEventListener('error', handleError);
      },
      { once: true }
    );
    scriptEl.addEventListener('error', handleError, { once: true });
    document.head.appendChild(scriptEl);
  }
}

/**
 * 获取客户端
 */
const getClient = () =>
  getObsToken().then((result) => {
    if (result && result.access) {
      opt = result;
      let promise: Promise<typeof import('esdk-obs-browserjs')>;
      if (getEnv('PROD')) {
        promise = new Promise((resolve, reject) => {
          appendScript(
            'ObsClient',
            `${getEnv(
              'BIZ_ORIGIN_PUBLIC_URL'
            )}/static/vendor/obs-client/esdk-obs-browserjs-3.21.8.min.js`,
            (factory) => {
              resolve({ default: factory });
            },
            reject
          );
        });
      } else {
        promise = import('esdk-obs-browserjs');
      }
      return promise.then(({ default: Client }) => {
        obsClient = new Client({
          access_key_id: result.access,
          secret_access_key: result.secret,
          security_token: result.token,
          server: result.obsHost,
        });
        return { config: result, instance: obsClient };
      });
    }
    return Promise.reject(new Error(i18n.t('build_upload_getOBSFail')));
  });

/**
 * 单例获取 OBS 客户端实例
 */
const getClientInstance = (): Promise<LoadingQueueResult> =>
  new Promise((resolve, reject) => {
    // 客户端不存在或 token 已过期，获取 token
    if (!obsClient || !opt || Date.now() > opt.expiresAt) {
      // 如果正在加载 token，保存所有异步操作
      if (loadingQueue) {
        loadingQueue.push([resolve, reject]);
      } else {
        // 获取新的客户端
        loadingQueue = [];
        getClient()
          .then((result) => {
            resolve(result);
            // 清空队列
            if (loadingQueue) {
              loadingQueue.forEach((fn) => {
                fn[0](result);
              });
            }
          })
          .catch((err) => {
            reject(err);
            // 清空队列
            if (loadingQueue) {
              loadingQueue.forEach((fn) => {
                fn[1](err);
              });
            }
          })
          .finally(() => {
            loadingQueue = null;
          });
      }
    } else {
      resolve({ config: opt, instance: obsClient });
    }
  });

/**
 * 根据对象实现基于 a 标签的下载功能
 * @param obj
 * @param name
 */
export const linkDownloadObj = (obj: File | Blob | string, name?: string) => {
  if (getEnv('SSR')) {
    return false;
  }

  const isUrl = isString(obj);
  if (!isUrl && !(window.URL || window.webkitURL)) {
    return false;
  }

  let filename = name;
  if (!filename) {
    if (isUrl) {
      filename = obj.substring(obj.lastIndexOf('/') + 1);
    } else if (obj instanceof File) {
      filename = obj.name;
    }
  }

  // 下载
  const linkNode = document.createElement('a');
  linkNode.target = '_blank';
  linkNode.href = isUrl ? obj : (window.URL || window.webkitURL).createObjectURL(obj);
  linkNode.download = filename || 'file';
  linkNode.click();
  // 销毁数据
  if (!isUrl) {
    (window.URL || window.webkitURL).revokeObjectURL(linkNode.href);
  }
  linkNode.remove();

  return true;
};

export const getFile = (url: string): Promise<Blob> => {
  const xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.responseType = 'blob';
  return new Promise((resolve, reject) => {
    xhr.onreadystatechange = () => {
      if (xhr.readyState === 4 && xhr.status === 200 && xhr.response instanceof Blob) {
        resolve(xhr.response);
      } else {
        reject();
      }
    };
    xhr.send(null);
  });
};

export const downloadObject = async (file: File | Blob | string, name?: string) => {
  let currentFile = file;
  let filename = name;
  if (isString(file)) {
    if (isImageUrl(file) || /\.(pdf|mp3|mp4|webp|webm)$/.test(file)) {
      currentFile = await getFile(file);
      if (!filename) {
        const lastIndex = file.lastIndexOf('/');
        const searchIndex = file.indexOf('?');
        filename = file.substring(
          lastIndex + 1,
          searchIndex > lastIndex ? searchIndex : file.length
        );
      }
    }
  }
  return linkDownloadObj(currentFile, filename) ? Promise.resolve() : Promise.reject();
};

/**
 * 下载文件
 * @param filePath
 * @param fileName
 */
export const download = (filePath: string, fileName?: string): Promise<void> => {
  const name = fileName || filePath.substring(filePath.lastIndexOf('/') + 1);
  const path = /^(https?:)?\/\//.test(filePath)
    ? filePath
    : `${getEnv('BIZ_ORIGIN_PUBLIC_URL')}/${filePath}`;

  if (path.indexOf(getEnv('BIZ_ORIGIN_PUBLIC_URL')) !== 0) {
    linkDownloadObj(path, name);
    return Promise.resolve();
  }

  return getClientInstance().then((result) => {
    if (result.instance) {
      return new Promise((resolve, reject) => {
        result.instance.getObject(
          {
            Key: path.replace(`${getEnv('BIZ_ORIGIN_PUBLIC_URL')}/`, ''),
            Bucket: result.config.bucket,
            SaveByType: 'arraybuffer',
          },
          (err, res) => {
            if (err) {
              reject(err);
            } else if (res.CommonMsg.Status < 300 && res.InterfaceResult) {
              linkDownloadObj(
                new Blob([res.InterfaceResult.Content as any], { type: 'arraybuffer' }),
                name
              );
              resolve();
            } else {
              reject(new Error(res.CommonMsg.Message));
            }
          }
        );
      });
    }
    return Promise.reject(new Error(i18n.t('build_upload_getClientFail')));
  });
};

export default getClientInstance;

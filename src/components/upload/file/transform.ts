import isString from 'lodash/isString';
import getEnv from '@/utils/env';

const urlArr: string[] = [];
/**
 * 文件转成可访问 URL
 * @param file
 */
export const fileToUrl = (file: MediaSource | Blob) => {
  if (!getEnv('SSR')) {
    const url = window.URL.createObjectURL(file);
    urlArr.push(url);
    return url;
  }
  return '';
};

/**
 * 销毁文件转换 URL
 * @param url
 */
export const revokeUrl = (url: string | number = 0) => {
  if (!getEnv('SSR')) {
    try {
      if (isString(url)) {
        const index = urlArr.indexOf(url);
        if (index !== -1) {
          urlArr.splice(index, 1);
        }
        window.URL.revokeObjectURL(url);
      } else {
        const revokeURL = urlArr[url];
        if (revokeURL) {
          urlArr.splice(url, 1);
          window.URL.revokeObjectURL(revokeURL);
        }
      }
    } catch (e) {
      console.warn(e);
    }
  }
};

/**
 * base64 字符串转为 File 对象
 * @param base64
 * @param filename
 */
export const base64ToFile = (base64: string, filename?: string) => {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || '';
  const bStr = atob(arr[1]);
  let len = bStr.length;
  const u8arr = new Uint8Array(len);
  len -= 1;
  while (len >= 0) {
    u8arr[len] = bStr.charCodeAt(len);
    len -= 1;
  }
  let name: string;
  if (typeof filename !== 'string') {
    const mimes = mime.split('/');
    name = !mimes.length ? 'file' : `file.${mimes[mimes.length - 1]}`;
  } else {
    name = filename;
  }
  return new File([u8arr], name, { type: mime });
};

/**
 * URL转为 File 对象
 * @param url
 * @param filename
 */
export const urlToFile = (url: string, filename?: string) =>
  new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('get', url, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        const file = xhr.response;
        let name =
          isString(filename) && filename ? filename : url.substring(url.lastIndexOf('/') + 1);
        if (!name) {
          name = 'file';
          if (file.type) {
            name += `.${file.type.substring(file.type.lastIndexOf('/') + 1)}`;
          }
        }
        resolve(new File([file], name, { type: file.type }));
      } else {
        reject();
      }
    };
    xhr.onerror = reject;
    xhr.send();
  });

/**
 * 获取视频首帧图片
 * @param file
 */
export const getVideoFirstFrameImage = (
  file: Blob | string
): Promise<{ url: string; width: number; height: number; duration: number }> =>
  new Promise((resolve) => {
    if (getEnv('SSR')) {
      resolve({ url: '', width: 0, height: 0, duration: 0 });
    } else {
      const video = document.createElement('video');
      const callback = (url = '') => {
        resolve({
          url,
          width: video.videoWidth,
          height: video.videoHeight,
          duration: Math.ceil(video.duration),
        });
        if (!isString(file)) {
          revokeUrl(video.src);
        }
      };
      video.src = isString(file) ? file : fileToUrl(file);
      video.style.position = 'fixed';
      video.style.top = '100%';
      video.style.left = '100%';
      video.addEventListener('loadeddata', () => {
        setTimeout(() => {
          const width = video.videoWidth;
          const height = video.videoHeight;
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(video, 0, 0, width, height);
            callback(canvas.toDataURL('image/jpeg'));
          }
          canvas.remove();
          video.remove();
        }, 500);
      });
      video.addEventListener('error', () => {
        callback();
        video.remove();
      });
      document.appendChild(video);
    }
  });

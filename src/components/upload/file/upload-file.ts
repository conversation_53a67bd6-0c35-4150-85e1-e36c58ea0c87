import i18n from 'i18next';
import type { ObsClientPutObjectParameter } from 'esdk-obs-browserjs';
import isBoolean from 'lodash/isBoolean';
import getClientInstance from './client';
import { EventProgressHandler, FileUploadStatus, generateFilePath, uuid } from './util';

class UploadFile {
  /**
   * 唯一标识ID
   */
  uid: string;

  /**
   * 文件名
   */
  name: string;

  /**
   * 文件大小
   */
  size: number;

  /**
   * 文字类型
   */
  type: string;

  /**
   * 文件上传状态
   */
  status: FileUploadStatus;

  /**
   * 文件上传路径
   */
  filePath: string;

  /**
   * 上传的源文件
   */
  raw: File | Blob;

  /**
   * 文件上传后访问地址
   */
  url: string;

  /**
   * 文件后缀
   */
  ext: string;

  /**
   * 监听上传进度
   */
  onProgress?: EventProgressHandler | null;

  constructor(file: File | Blob, onProgress?: EventProgressHandler) {
    const name = 'name' in file ? file.name : '';
    this.uid = uuid();
    this.name = name;
    this.ext = name ? name.substring(name.lastIndexOf('.') + 1) : '';
    this.size = file.size;
    this.type = file.type;
    this.status = 'waiting';
    this.raw = file;
    this.filePath = generateFilePath(this.name);
    this.url = '';
    this.onProgress = onProgress;
  }

  /**
   * 删除文件上传地址
   */
  async remove(): Promise<void | Error> {
    try {
      // 获取客户端实例
      const { instance, config } = await getClientInstance();
      return new Promise((resolve) => {
        instance.deleteObject(
          {
            Bucket: config.bucket,
            Key: this.filePath,
          },
          (error, result) => {
            if (error) {
              resolve(error);
            } else if (result.CommonMsg.Status < 300) {
              resolve();
            } else {
              resolve(new Error(result.CommonMsg.Message));
            }
          }
        );
      });
    } catch (e) {
      const failMsg = this.status === 'cancel' ? i18n.t('public_cancel') : i18n.t('public_delete');
      return Promise.resolve(new Error(i18n.t('build_upload_deleteOrCancleFail', { failMsg })));
    }
  }

  /**
   * 上传文件
   */
  async upload(): Promise<UploadFile> {
    // 上传完成的文件不再上传
    if (this.status === 'done') {
      return Promise.resolve(this);
    }
    // 如果上传已被取消过,返回宝座
    if (this.status === 'cancel') {
      return Promise.reject(new Error(i18n.t('build_upload_cancle')));
    }

    // 禁止上次 .htm/.html
    if (/\.html?$/i.test(this.name)) {
      return Promise.reject(new Error(i18n.t('build_upload_forbid')));
    }

    // 更新上传的状态
    this.status = 'uploading';
    try {
      // 获取客户端实例
      const { instance, config } = await getClientInstance();
      return new Promise((resolve, reject) => {
        const { filePath } = this;
        const parameter: ObsClientPutObjectParameter = {
          Bucket: config.bucket,
          SourceFile: this.raw,
          Key: filePath,
          // 添加进度监听事件
          ProgressCallback: (uploadSize, totalSize, duration) => {
            // 当前状态不为取消时
            if (this.status !== 'cancel' && this.onProgress) {
              this.onProgress({
                uploadSize,
                totalSize,
                duration,
                percent: (uploadSize / totalSize) * 100,
              });
            }
          },
        };
        // 上传文件
        instance.putObject(parameter, (error, result) => {
          if (error) {
            this.status = 'error';
            reject(error);
          } else {
            const { CommonMsg } = result;
            if (CommonMsg.Status < 200 || CommonMsg.Status >= 300) {
              this.status = 'error';
              const err = new Error(CommonMsg.Message);
              reject(err);
            }
            // 如果本次上传被取消,上传成功后删除文件
            else if (this.status === 'cancel') {
              this.remove().finally(() => {
                reject(new Error(i18n.t('build_upload_cancle')));
              });
            } else {
              this.status = 'done';
              this.url = `${config.filePath}${filePath}`;
              resolve(this);
            }
          }
        });
      });
    } catch (e) {
      return Promise.reject(new Error(i18n.t('build_upload_fail')));
    }
  }

  /**
   * 修改文件上传状态为取消
   */
  cancel() {
    if (this.status === 'uploading') {
      this.status = 'cancel';
      return Promise.resolve();
    }
    return Promise.resolve(new Error(i18n.t('build_upload_isUpload')));
  }

  on(eventName: 'process', fn: EventProgressHandler) {
    if (eventName === 'process') {
      this.onProgress = fn;
    }
  }
}

/* eslint-disable no-unused-vars */
interface GenerateUploadFile {
  (file: File | Blob): UploadFile;
  (file: File | Blob, onProgress: EventProgressHandler): UploadFile;
  (file: File | Blob, autoUpload: boolean): UploadFile;
  (file: File | Blob, onProgress: EventProgressHandler, autoUpload: boolean): UploadFile;
}
/* eslint-enable */

/**
 * 生成上传文件
 * @param file {File|Blob} 要上传的文件
 * @param onProgress {EventProgressHandler|boolean} 监听进度/是否自动上传
 * @param autoUpload {boolean} 是否自动上传
 */
export const generateUploadFile: GenerateUploadFile = (
  file: File | Blob,
  onProgress?: EventProgressHandler | boolean,
  autoUpload = true
) => {
  let isUpload: boolean;
  let handleProgress: EventProgressHandler | undefined;
  if (isBoolean(onProgress)) {
    isUpload = onProgress;
  } else {
    handleProgress = onProgress;
    isUpload = autoUpload as boolean;
  }
  const uploadFile = new UploadFile(file, handleProgress);
  if (isUpload) {
    uploadFile.upload();
  }
  return uploadFile;
};

export default UploadFile;

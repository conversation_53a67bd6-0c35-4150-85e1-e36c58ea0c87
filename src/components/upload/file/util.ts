import isString from 'lodash/isString';
import dayjs from 'dayjs';
import getEnv from '@/utils/env';

export const IMAGE_EXT_REGEXP = /\.(a?png|jpe?g|gif|svgz?|ico|bmp|webp|xbm|tif|pjp)$/;

export type FileUploadStatus = 'waiting' | 'uploading' | 'done' | 'error' | 'cancel';

export type EventProgressHandler = any;

/**
 * 生成唯一标识
 */
export function uuid() {
  return `${Date.now()}${Math.floor(Math.random() * 1000)}`;
}

/**
 * 获取文件后缀名
 * @param file
 */
export const getExt = (file: string | File | Blob | { name?: string; url?: string }) => {
  let filePath;
  if (isString(file)) {
    filePath = file;
  } else if ((file as { name?: string }).name) {
    filePath = (file as { name?: string }).name;
  } else if ((file as { url?: string }).url) {
    filePath = (file as { url?: string }).url;
  }
  if (filePath) {
    const startIndex = filePath.lastIndexOf('.');
    return startIndex !== -1
      ? filePath.substring(startIndex + 1, filePath.length).toLowerCase()
      : '';
  }
  return '';
};

/**
 * 生成新的文件路径
 * @param fileName
 */
export const generateFilePath = (fileName: string) => {
  const now = dayjs();
  return `${getEnv('BIZ_OBS_FOLDER')}/${now.format('YYYYMMDD')}/${now.valueOf()}${Math.floor(
    Math.random() * 1000
  )}.${getExt(fileName)}`;
};

const fileSize = {
  kb: 1024,
  mb: 1024 * 1024,
  gb: 1024 * 1024 * 1024,
};

/**
 * 格式化文件大小
 * @param size
 */
export const formatFileSize = (size: number | string) => {
  if (isString(size)) {
    return size;
  }
  if (size > fileSize.mb) {
    if (size > fileSize.gb) {
      return `${(size / fileSize.gb).toFixed(2)}GB`;
    }
    return `${(size / fileSize.mb).toFixed(2)}MB`;
  }
  return `${(size / fileSize.kb).toFixed(2)}KB`;
};

/**
 * 判断是否是图片URL
 * @param value
 */
export const isImageUrl = (value: unknown) =>
  typeof value === 'string' && IMAGE_EXT_REGEXP.test(value.toLowerCase());

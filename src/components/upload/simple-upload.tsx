/* eslint-disable no-undef */
import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drag<PERSON><PERSON><PERSON><PERSON><PERSON>,
  forward<PERSON><PERSON>,
  HTM<PERSON><PERSON>ributes,
  PropsWithChildren,
  useCallback,
  useImperativeHandle,
  useRef,
  MouseEvent,
} from 'react';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isNil from 'lodash/isNil';
import attrAccept from 'rc-upload/es/attr-accept';
import traverseFileTree from 'rc-upload/es/traverseFileTree';
import { RcFile } from 'rc-upload/es/interface';
import { EventProgressHandler, generateUploadFile, UploadFile } from './file';
import styles from './simple-upload.module.less';

// 空方法
export const EMPTY_FN = () => {};

declare module 'react' {
  interface InputHTMLAttributes<T> extends HTMLAttributes<T> {
    webkitdirectory?: boolean | string;
  }
}

export type FileUploadStatus = 'waiting' | 'uploading' | 'done' | 'error' | 'cancel';

export interface FileListItem {
  raw?: File | Blob;
  uid: string | number;
  name: string;
  url: string;
  status: FileUploadStatus;
  size?: number | string;
  type?: string;
  ext?: string;
  onProgress?: EventProgressHandler | null;
  filePath?: string;
}

export type UploadStatus = 'success' | 'error' | 'cancel';
// @ts-ignore
export type FileBeforeUpload = MultipleParamsFn<
  [file: UploadFile, fileList: FileListItem[]],
  boolean
>;
// @ts-ignore
export type FileUploadChangeHandler = MultipleParamsFn<
  [event: { status: UploadStatus; file: UploadFile; message?: string }]
>;

// @ts-ignore
export interface SimpleUploadProps extends HTMLAttributes<HTMLSpanElement> {
  accept?: string;
  beforeUpload?: FileBeforeUpload;
  maxCount?: number;
  multiple?: boolean;
  disabled?: boolean;
  directory?: boolean;
  onProgress?: EventProgressHandler;
  onChange?: FileUploadChangeHandler;
  // @ts-ignore
  onFolder?: MultipleParamsFn<[folderName: string, fileList: FileListItem[]]>;
}

export interface SimpleUploadInstance {
  // @ts-ignore
  getFileList: SimpleFn<void, FileListItem[]>;
  // @ts-ignore
  upload: MultipleParamsFn<[file: File | Blob | (File | Blob)[]]>;
  // @ts-ignore
  selectFile: SimpleFn;
}

const SimpleUpload = forwardRef<SimpleUploadInstance, PropsWithChildren<SimpleUploadProps>>(
  (
    {
      children,
      accept,
      multiple,
      maxCount,
      disabled,
      className,
      directory,
      beforeUpload,
      onProgress,
      onChange,
      onFolder,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    // const { fileList, dispatchFileList } = useContext(FileListContext);
    const uploadFileList = useRef<FileListItem[]>([]);
    const fileEl = useRef(null as unknown as HTMLInputElement);
    // 上传文件
    const uploadFiles = useCallback(
      (files: (File | Blob)[] | FileList) => {
        const tmpUploadFileList: UploadFile[] = [];
        const updateFileList = !uploadFileList.current.length;

        if (maxCount === 1) {
          tmpUploadFileList.push(generateUploadFile(files[0], false));
          uploadFileList.current = tmpUploadFileList;
        } else {
          const maxLen = Math.min(
            files.length,
            Math.abs(maxCount!) - uploadFileList.current.length
          );
          for (let i = 0; i < maxLen; i += 1) {
            tmpUploadFileList.push(generateUploadFile(files[i], false));
          }
          uploadFileList.current = uploadFileList.current.concat(tmpUploadFileList);
        }
        // dispatchFileList({ type: 'set', payload: uploadFileList.current });
        tmpUploadFileList.forEach((uploadFile) => {
          if (!beforeUpload || beforeUpload(uploadFile, uploadFileList.current)) {
            if (onProgress) {
              uploadFile.on('process', onProgress);
            }
            uploadFile
              .upload()
              .then(() => {
                if (updateFileList) {
                  // dispatchFileList({ type: 'set', payload: uploadFileList.current });
                }
                onChange!({ status: 'success', file: uploadFile });
              })
              .catch((err) => {
                onChange!({
                  status: 'error',
                  file: uploadFile,
                  message: err.message || t('build_upload_fail'),
                });
              });
          } else {
            // dispatchFileList({ type: 'remove', payload: uploadFile });
          }
        });
      },
      // eslint-disable-next-line
      [beforeUpload, maxCount, onChange, onProgress]
    );
    // 文件列表改变
    const handleChangeInput = useCallback<ChangeEventHandler<HTMLInputElement>>(
      (e) => {
        const { files } = e.currentTarget;
        if (files && files.length !== 0) {
          uploadFiles(files);
          if (directory && onFolder) {
            const file = files[0];
            if (file.webkitRelativePath) {
              const folderNames = file.webkitRelativePath.split('/');
              onFolder(folderNames[0] || '', uploadFileList.current);
            }
          }
        }
        // 清除文件，解决重复上传同一个文件不触发 change 事件的问题
        fileEl.current.value = '';
      },
      [directory, onFolder, uploadFiles]
    );
    // 文件/文件夹拖拽
    const handleDrop: DragEventHandler<HTMLSpanElement> = useCallback(
      (e) => {
        e.preventDefault();

        if (e.type === 'dragover') {
          return;
        }

        if (directory) {
          traverseFileTree(
            Array.prototype.slice.call(e.dataTransfer.items),
            uploadFiles,
            (file: RcFile) => attrAccept(file, accept || '')
          );
        } else {
          let files = [...e.dataTransfer.files].filter((file) =>
            attrAccept(file as RcFile, accept || '')
          );
          if (multiple === false) {
            files = files.slice(0, 1);
          }
          uploadFiles(files);
        }
      },
      [accept, directory, multiple, uploadFiles]
    );
    // 选择文件
    const selectFile = useCallback(
      (e: MouseEvent) => {
        e.stopPropagation();
        if (disabled) return;
        if (directory) {
          const inputEl = document.createElement('input');
          if (isNil(inputEl.webkitdirectory)) {
            message.error(t('build_upload_browserUpdate'));
            inputEl.remove();
            return;
          }
          inputEl.remove();
        }
        fileEl.current.click();
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [directory, disabled, t]
    );

    // useEffect(() => {
    //   uploadFileList.current = fileList;
    // }, [fileList]);

    useImperativeHandle(
      ref,
      () => ({
        selectFile,
        getFileList: () => uploadFileList.current,
        upload: (file: any) => {
          if (isArray(file)) {
            uploadFiles(file);
          } else {
            uploadFiles([file]);
          }
        },
      }),
      [selectFile, uploadFiles]
    );

    const classes = classNames(styles.upload, className);
    return (
      <span
        {...props}
        tabIndex={0}
        role="button"
        className={classes}
        onClick={selectFile}
        onDrop={handleDrop}
        onDragOver={handleDrop}
      >
        <input
          ref={fileEl}
          type="file"
          accept={accept}
          multiple={multiple}
          className={styles.input}
          onChange={handleChangeInput}
          // eslint-disable-next-line
          webkitdirectory={directory ? 'webkitdirectory' : undefined}
        />
        {children}
      </span>
    );
  }
);

SimpleUpload.displayName = 'EchSimpleUpload';

SimpleUpload.defaultProps = {
  accept: '',
  multiple: false,
  maxCount: Number.MAX_SAFE_INTEGER,
  disabled: false,
  directory: false,
  beforeUpload: undefined,
  onProgress: undefined,
  onChange: EMPTY_FN,
  onFolder: EMPTY_FN,
};

export default SimpleUpload;

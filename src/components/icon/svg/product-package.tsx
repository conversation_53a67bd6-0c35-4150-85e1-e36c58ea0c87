import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const productPackageIcon: IconComponentProps['icon'] = {
  name: 'productPackage',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 24 26', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M0,7.58448L0,23.18Q0,24.0581,0.620934,24.6791Q1.24187,25.3,2.12,25.3L21.56,25.3Q22.4381,25.3,23.0591,24.6791Q23.68,24.0581,23.68,23.18L23.68,7.58448L21.1094,1.31567Q20.8647,0.718757,20.3289,0.359379Q19.7931,0,19.1479,0L4.53207,0Q3.88692,0,3.35113,0.359379Q2.81535,0.718758,2.57058,1.31567L0,7.58448ZM9.39586,1L4.53207,1Q3.78083,1,3.49581,1.69507L1.24543,7.18302L7.36365,7.18302L7.49431,7.18301L9.39586,1ZM13.2379,1L10.4421,1L8.54053,7.18302L15.1395,7.18302L13.2379,1ZM16.3164,7.18302L22.4346,7.18302L20.1842,1.69507Q19.8992,1,19.1479,1L14.2842,1L16.1857,7.18301L16.3164,7.18302ZM16.4933,8.18302L16.3164,8.18302L16.3164,14.6215Q16.3164,15.4996,15.6954,16.1206Q15.0745,16.7415,14.1964,16.7415L9.48365,16.7415Q8.60551,16.7415,7.98458,16.1206Q7.36365,15.4996,7.36365,14.6215L7.36365,8.18302L1,8.18302L1,23.18Q1,23.6439,1.32804,23.972Q1.65608,24.3,2.12,24.3L21.56,24.3Q22.0239,24.3,22.352,23.972Q22.68,23.6439,22.68,23.18L22.68,8.18302L16.4933,8.18302ZM8.36365,8.18301L8.36365,14.6215Q8.36365,15.0854,8.69169,15.4135Q9.01973,15.7415,9.48365,15.7415L14.1964,15.7415Q14.6603,15.7415,14.9883,15.4135Q15.3164,15.0854,15.3164,14.6215L15.3164,8.18301L8.36365,8.18301Z',
        },
      },
    ],
  },
};

function ProductPackage(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={productPackageIcon} />;
}

ProductPackage.displayName = 'EchFilterMenuIcon';

export default ProductPackage;

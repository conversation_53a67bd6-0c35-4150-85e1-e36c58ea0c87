import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const statisticsIcon: IconComponentProps['icon'] = {
  name: 'statistics',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 24 27', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M32.6667,10L32.6667,23.4584Q32.6667,24.6493,31.8246,25.4914Q30.9826,26.3334,29.7917,26.3334L6.83334,26.3334Q6.78409,26.3334,6.73579,26.3238Q6.68749,26.3142,6.64199,26.2954Q6.5965,26.2765,6.55555,26.2491Q6.5146,26.2218,6.47978,26.187Q6.44496,26.1521,6.4176,26.1112Q6.39024,26.0703,6.3714,26.0248Q6.35255,25.9793,6.34294,25.931Q6.33334,25.8827,6.33334,25.8334Q6.33334,25.7842,6.34294,25.7359Q6.35255,25.6876,6.3714,25.6421Q6.39024,25.5966,6.4176,25.5556Q6.44496,25.5147,6.47978,25.4799Q6.5146,25.445,6.55555,25.4177Q6.5965,25.3903,6.64199,25.3715Q6.68749,25.3526,6.73579,25.343Q6.78409,25.3334,6.83334,25.3334L29.7917,25.3334Q30.5683,25.3334,31.1175,24.7843Q31.6667,24.2351,31.6667,23.4584L31.6667,10.5L1,10.5L1,15.5417Q1,15.591,0.9903930000000001,15.6393Q0.980785,15.6876,0.96194,15.7331Q0.943094,15.7786,0.915735,15.8195Q0.888375,15.8605,0.853553,15.8953Q0.818731,15.9301,0.777785,15.9575Q0.736839,15.9848,0.691342,16.003700000000002Q0.645845,16.0225,0.5975451,16.0321Q0.5492457,16.0417,0.5,16.0417Q0.4507543,16.0417,0.4024549,16.0321Q0.354155,16.0225,0.308658,16.003700000000002Q0.263161,15.9848,0.222215,15.9575Q0.181269,15.9301,0.146447,15.8953Q0.11162499999999997,15.8605,0.08426499999999998,15.8195Q0.05690600000000001,15.7786,0.03805999999999998,15.7331Q0.019214999999999982,15.6876,0.009606999999999977,15.6393Q0,15.591,0,15.5417L0,10L0,8.41675L0,2.875Q0,1.68414,0.842069,0.842069Q1.68414,0,2.875,0L29.7917,0Q30.9825,0,31.8246,0.842071Q32.6667,1.68413,32.6667,2.875L32.6667,10L32.6667,10ZM1,8.41652L1,8.416979999999999L1,9.5L31.6667,9.5L31.6667,2.875Q31.6667,2.0983400000000003,31.1175,1.54917Q30.5683,1,29.7917,1L2.875,1Q2.09835,1,1.54918,1.54918Q1,2.09835,1,2.875L1,8.41652ZM6.12938,5.75L7.71272,5.75Q7.76196,5.75,7.81026,5.74039Q7.85856,5.73078,7.90406,5.71194Q7.94955,5.69309,7.9905,5.66573Q8.03145,5.63837,8.06627,5.60355Q8.10109,5.56873,8.12845,5.52778Q8.155809999999999,5.48684,8.17465,5.44134Q8.1935,5.39584,8.203109999999999,5.34755Q8.212710000000001,5.29925,8.212720000000001,5.25Q8.212710000000001,5.20075,8.203109999999999,5.15245Q8.1935,5.10416,8.17465,5.05866Q8.155809999999999,5.01316,8.12845,4.97221Q8.10109,4.93127,8.06627,4.89645Q8.03145,4.86162,7.9905,4.83426Q7.94955,4.80691,7.90406,4.78806Q7.85856,4.76921,7.81026,4.75961Q7.76196,4.75,7.71272,4.75L6.12938,4.75Q6.08013,4.75,6.03183,4.75961Q5.98353,4.76921,5.93804,4.78806Q5.89254,4.80691,5.85159,4.83426Q5.81065,4.86162,5.77583,4.89645Q5.741,4.93127,5.71364,4.97221Q5.68628,5.01316,5.66744,5.05866Q5.64859,5.10416,5.63899,5.15245Q5.62938,5.20075,5.62938,5.25Q5.62938,5.29925,5.63899,5.34755Q5.64859,5.39584,5.66744,5.44134Q5.68628,5.48684,5.71364,5.52778Q5.741,5.56873,5.77583,5.60355Q5.81065,5.63837,5.85159,5.66573Q5.89254,5.69309,5.93804,5.71194Q5.98353,5.73078,6.03183,5.74039Q6.08013,5.75,6.12938,5.75ZM12.4635,5.75L26.7135,5.75Q26.7628,5.75,26.8111,5.74039Q26.8593,5.73078,26.9048,5.71194Q26.9503,5.69309,26.9913,5.66573Q27.0322,5.63837,27.0671,5.60355Q27.1019,5.56873,27.1292,5.52778Q27.1566,5.48684,27.1754,5.44134Q27.1943,5.39584,27.2039,5.34755Q27.2135,5.29925,27.2135,5.25Q27.2135,5.20075,27.2039,5.15245Q27.1943,5.10416,27.1754,5.05866Q27.1566,5.01316,27.1292,4.97221Q27.1019,4.93127,27.0671,4.89645Q27.0322,4.86162,26.9913,4.83426Q26.9503,4.80691,26.9048,4.78806Q26.8593,4.76921,26.8111,4.75961Q26.7628,4.75,26.7135,4.75L12.4635,4.75Q12.4143,4.75,12.366,4.75961Q12.3177,4.76921,12.2722,4.78806Q12.2267,4.80691,12.1857,4.83426Q12.1448,4.86162,12.11,4.89645Q12.0751,4.93127,12.0478,4.97221Q12.0204,5.01316,12.0016,5.05866Q11.9827,5.10416,11.9731,5.15245Q11.9635,5.20075,11.9635,5.25Q11.9635,5.29925,11.9731,5.34755Q11.9827,5.39584,12.0016,5.44134Q12.0204,5.48684,12.0478,5.52778Q12.0751,5.56873,12.11,5.60355Q12.1448,5.63837,12.1857,5.66573Q12.2267,5.69309,12.2722,5.71194Q12.3177,5.73078,12.366,5.74039Q12.4143,5.75,12.4635,5.75ZM21.7856,15.4302L25.0022,18.566Q25.0374,18.6004,25.0787,18.6272Q25.12,18.6541,25.1658,18.6723Q25.2115,18.6906,25.2599,18.6996Q25.3083,18.7086,25.3576,18.708Q25.4068,18.7073,25.455,18.6971Q25.5032,18.6869,25.5484,18.6675Q25.5937,18.648,25.6343,18.6202Q25.6749,18.5923,25.7092,18.557Q25.7436,18.5218,25.7704,18.4805Q25.7973,18.4392,25.8155,18.3935Q25.8338,18.3477,25.8428,18.2993Q25.8518,18.2509,25.8512,18.2016Q25.8505,18.1524,25.8403,18.1042Q25.8301,18.0561,25.8107,18.0108Q25.7913,17.9655,25.7634,17.925Q25.7355,17.8844,25.7002,17.85L22.1532,14.392Q22.1197,14.3594,22.0808,14.3335Q22.0419,14.3077,21.9988,14.2895Q21.9558,14.2713,21.9101,14.2614Q21.8645,14.2515,21.8178,14.2502Q21.771,14.2489,21.7249,14.2563Q21.6788,14.2637,21.6348,14.2796Q21.5908,14.2954,21.5506,14.3191Q21.5103,14.3428,21.4751,14.3735L15.452,19.638L10.5738,14.9662Q10.5386,14.9325,10.49753,14.9063Q10.4565,14.88,10.41116,14.8621Q10.36583,14.8443,10.3179,14.8355Q10.26997,14.8268,10.22125,14.8274Q10.17253,14.8281,10.12485,14.8381Q10.07717,14.8481,10.03233,14.8672Q9.98748,14.8863,9.94717,14.9136Q9.90685,14.941,9.87257,14.9756L0.23355599999999999,24.7138Q0.19891199999999998,24.7488,0.171763,24.7898Q0.144613,24.8309,0.12600099999999997,24.8765Q0.10738799999999998,24.9221,0.098028,24.9705Q0.08866800000000002,25.0188,0.08892,25.0681Q0.089171,25.1173,0.099026,25.1655Q0.10887999999999998,25.2138,0.12795800000000002,25.2592Q0.147036,25.3046,0.174605,25.3454Q0.202173,25.3862,0.23717300000000002,25.4209Q0.307857,25.4908,0.399936,25.5284Q0.49201505,25.566,0.5914697,25.5655Q0.690925,25.565,0.7826150000000001,25.5265Q0.874305,25.4879,0.9442699999999999,25.4172L10.23731,16.0286L15.0907,20.6766Q15.1243,20.7087,15.1632,20.7341Q15.202,20.7595,15.2449,20.7773Q15.2878,20.7951,15.3333,20.8047Q15.3787,20.8143,15.4252,20.8154Q15.4716,20.8164,15.5174,20.8089Q15.5633,20.8014,15.6069,20.7856Q15.6506,20.7697,15.6906,20.7461Q15.7306,20.7225,15.7656,20.692L21.7856,15.4302Z',
        },
      },
    ],
  },
};
function Statistics(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={statisticsIcon} />;
}

Statistics.displayName = 'EchStatisticsIcon';

export default Statistics;

import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const articleIcon: IconComponentProps['icon'] = {
  name: 'article',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 22 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M0.103392,1.26316L16.4738,1.26316L16.4738,0L0.103392,0L0.103392,1.26316ZM0,9L0,3.63228L0.819441,3.63228L0.819441,5.89514L3.6311,5.89514L3.6311,3.63228L4.45054,3.63228L4.45054,9L3.6311,9L3.6311,6.5943L0.819441,6.5943L0.819441,9L0,9ZM8.28845,5.05272L16.4736,5.05272L16.4736,4.42114L8.28845,4.42114L8.28845,5.05272ZM5.61776,6.81614C5.79067,6.62067,6.02372,6.5267,6.3094,6.5267C6.565,6.5267,6.77926,6.59812,6.94465,6.74848C7.10629,6.89883,7.18898,7.09054,7.18898,7.32735C7.18898,7.5604,7.09877,7.7709,6.92586,7.9626C6.82061,8.07161,6.63642,8.21445,6.36954,8.39488C6.08011,8.58658,5.9072,8.75573,5.85081,8.90233L7.19274,8.90233L7.19274,9.26318L5.34336,9.26318C5.34336,8.9963,5.42982,8.76325,5.61024,8.56027C5.70797,8.44374,5.91095,8.28211,6.21543,8.07161C6.39585,7.94381,6.52366,7.84232,6.59883,7.76338C6.71912,7.62806,6.78302,7.48146,6.78302,7.32359C6.78302,7.17323,6.74167,7.06047,6.65898,6.98529C6.57628,6.91011,6.45224,6.87252,6.29436,6.87252C6.12521,6.87252,5.99741,6.9289,5.91095,7.04543C5.82074,7.1582,5.77563,7.31983,5.76812,7.53785L5.3584,7.53785C5.36215,7.23713,5.44861,6.99656,5.61776,6.81614ZM8.28845,8.21044L16.4736,8.21044L16.4736,7.57886L8.28845,7.57886L8.28845,8.21044ZM0.103392,11.3684L16.4738,11.3684L16.4738,10.7368L0.103392,10.7368L0.103392,11.3684ZM0,19.1052L0,13.7375L0.819441,13.7375L0.819441,16.0004L3.6311,16.0004L3.6311,13.7375L4.45054,13.7375L4.45054,19.1052L3.6311,19.1052L3.6311,16.6995L0.819441,16.6995L0.819441,19.1052L0,19.1052ZM8.28845,14.5264L16.4736,14.5264L16.4736,13.8948L8.28845,13.8948L8.28845,14.5264ZM5.61776,16.9214C5.79067,16.7259,6.02372,16.6319,6.3094,16.6319C6.565,16.6319,6.77926,16.7033,6.94465,16.8537C7.10629,17.0041,7.18898,17.1958,7.18898,17.4326C7.18898,17.6656,7.09877,17.8761,6.92586,18.0678C6.82061,18.1768,6.63642,18.3197,6.36954,18.5001C6.08011,18.6918,5.9072,18.861,5.85081,19.0076L7.19274,19.0076L7.19274,19.3684L5.34336,19.3684C5.34336,19.1015,5.42982,18.8685,5.61024,18.6655C5.70797,18.549,5.91095,18.3873,6.21543,18.1768C6.39585,18.049,6.52366,17.9475,6.59883,17.8686C6.71912,17.7333,6.78302,17.5867,6.78302,17.4288C6.78302,17.2785,6.74167,17.1657,6.65898,17.0905C6.57628,17.0153,6.45224,16.9777,6.29436,16.9777C6.12521,16.9777,5.99741,17.0341,5.91095,17.1507C5.82074,17.2634,5.77563,17.4251,5.76812,17.6431L5.3584,17.6431C5.36215,17.3424,5.44861,17.1018,5.61776,16.9214ZM8.28845,18.3159L16.4736,18.3159L16.4736,17.6843L8.28845,17.6843L8.28845,18.3159ZM0.103392,21.4736L16.4738,21.4736L16.4738,20.842L0.103392,20.842L0.103392,21.4736Z',
        },
      },
    ],
  },
};
function Article(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={articleIcon} />;
}

Article.displayName = 'EchArticleIcon';

export default Article;

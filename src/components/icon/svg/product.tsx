import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const productIcon: IconComponentProps['icon'] = {
  name: 'product',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 24 27', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M6.91285,5.3L17.0872,5.3Q16.9676,3.60275,15.7324,2.36759Q14.3649,1,12.4308,1L11.5693,1Q9.6352,1,8.26761,2.36759Q7.03244,3.60275,6.91285,5.3ZM18.0895,5.3L22.4,5.3C23.2837,5.3,24,6.01635,24,6.9L24,25.3C24,26.1837,23.2837,26.9,22.4,26.9L1.6,26.9C0.716344,26.9,0,26.1837,0,25.3L0,6.9C0,6.01635,0.716344,5.3,1.6,5.3L5.91053,5.3Q6.0321,3.18888,7.5605,1.66048Q9.22098,0,11.5693,0L12.4308,0Q14.7791,0,16.4395,1.66048Q17.968,3.18888,18.0895,5.3ZM1.17574,25.7243Q1,25.5485,1,25.3L1,6.9Q1,6.65147,1.17574,6.47574Q1.35147,6.3,1.6,6.3L22.4,6.3Q22.6485,6.3,22.8243,6.47574Q23,6.65148,23,6.9L23,25.3Q23,25.5485,22.8243,25.7243Q22.6485,25.9,22.4,25.9L1.6,25.9Q1.35147,25.9,1.17574,25.7243ZM7.12094,20.379Q5.09998,18.3581,5.09998,15.5C5.09998,15.2239,5.32383,15,5.59998,15C5.87612,15,6.09998,15.2239,6.09998,15.5Q6.09998,17.9439,7.82805,19.6719Q9.55611,21.4,12,21.4Q14.4438,21.4,16.1719,19.6719Q17.8996,17.9442,17.9,15.5C17.9,15.3674,17.9527,15.2402,18.0464,15.1464C18.1402,15.0527,18.2674,15,18.4,15C18.6761,15,18.9,15.2239,18.9,15.5Q18.9,18.3581,16.879,20.379Q14.858,22.4,12,22.4Q9.1419,22.4,7.12094,20.379Z',
        },
      },
    ],
  },
};
function Product(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={productIcon} />;
}

Product.displayName = 'EchProductIcon';

export default Product;

import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const filterMenuIcon: IconComponentProps['icon'] = {
  name: 'filterMenu',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M12.3,17.25L12.3,9.44L19.32,1.24C19.44,1.1,19.5,0.930003,19.5,0.750003C19.5,0.550003,19.42,0.360003,19.28,0.220003C19.14,0.0800027,18.95,0.00000265252,18.75,0.00000265252L0.75,0.00000265252C0.57,0.00000265252,0.4,0.0600027,0.26,0.180003C0.11,0.310003,0.02,0.500003,1.13687e-13,0.690003C-0.01,0.890003,0.05,1.09,0.18,1.24L7.2,9.44L7.2,15.47C7.2,15.76,7.36,16.02,7.62,16.15L11.22,17.92L11.55,18C11.97,18,12.3,17.66,12.3,17.25ZM10.8002,16.04L10.8002,9.16001C10.8002,8.98001,10.8602,8.81001,10.9802,8.67001L17.1202,1.50001L2.38019,1.50001L8.52019,8.67001C8.64019,8.81001,8.70019,8.98001,8.70019,9.16001L8.70019,15.01L10.8002,16.04Z',
        },
      },
    ],
  },
};

function FilterMenu(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={filterMenuIcon} />;
}

FilterMenu.displayName = 'EchFilterMenuIcon';

export default FilterMenu;

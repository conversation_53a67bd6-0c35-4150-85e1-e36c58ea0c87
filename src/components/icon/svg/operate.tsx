import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const OperateIcon: IconComponentProps['icon'] = {
  name: 'operate',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 14 14', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M11.0195,0.550377L13.9594,5.35018C14.1727,5.70216,14.1727,6.13734,13.9594,6.48933L11.0195,11.2891C10.8062,11.6283,10.4262,11.8395,10.0129,11.8395L4.1065,11.8395C3.69318,11.8395,3.3132,11.6283,3.09987,11.2891L0.159993,6.48933C-0.0533311,6.13734,-0.0533311,5.70216,0.159993,5.35018L3.10654,0.550377C3.3132,0.211191,3.69318,0,4.1065,0L10.0129,0C10.4262,0,10.8062,0.211191,11.0195,0.550377ZM4.1099,0.95868L10.0163,0.95868C10.0763,0.95868,10.1296,0.990679,10.1563,1.03548L13.0962,5.83528C13.1295,5.88647,13.1295,5.95047,13.0962,6.00167L10.1563,10.8015C10.1296,10.8463,10.0763,10.8783,10.0163,10.8783L4.1099,10.8783C4.0499,10.8783,3.99657,10.8463,3.96991,10.8015L1.02336,6.00167C0.996698,5.95047,0.996698,5.88647,1.02336,5.83528L3.96991,1.03548C3.99657,0.990679,4.0499,0.95868,4.1099,0.95868ZM7.06277,3.83664C8.25606,3.83664,9.22935,4.771,9.22935,5.91655C9.22935,7.06211,8.25606,7.99647,7.06277,7.99647C5.86948,7.99647,4.89618,7.06211,4.89618,5.91655C4.89618,4.771,5.86948,3.83664,7.06277,3.83664ZM5.89614,5.91655C5.89614,5.62217,6.01614,5.33418,6.23613,5.12299C6.45612,4.9118,6.75612,4.7966,7.06277,4.7966C7.70941,4.7966,8.22939,5.29578,8.22939,5.91655C8.22939,6.53733,7.70941,7.03651,7.06277,7.03651C6.41612,7.03651,5.89614,6.53733,5.89614,5.91655Z',
        },
      },
    ],
  },
};

function Operate(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={OperateIcon} />;
}

Operate.displayName = 'EchOperateIcon';

export default Operate;

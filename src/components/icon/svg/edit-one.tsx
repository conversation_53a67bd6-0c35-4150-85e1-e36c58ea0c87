import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const EditOneIcon: IconComponentProps['icon'] = {
  name: 'editOne',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 14 14', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M4.27316,10.3262L1.83326,10.3262C1.55993,10.3262,1.33328,10.1062,1.33328,9.82626L1.33328,7.39969C1.33328,7.27303,1.38661,7.1397,1.47994,7.05304L8.37965,0.139994C8.57298,-0.0466647,8.8863,-0.0466647,9.08629,0.139994L11.5195,2.57989C11.6129,2.67322,11.6662,2.79988,11.6662,2.93321C11.6662,3.05987,11.6129,3.18653,11.5195,3.28653L4.62647,10.1796C4.53314,10.2796,4.40648,10.3262,4.27316,10.3262ZM10.4582,2.92756L4.06516,9.32728L2.3319,9.32728L2.3319,7.60735L8.73164,1.20764L10.4582,2.92756ZM12.4995,11.9958C12.7728,11.9958,12.9995,12.2225,12.9995,12.4958C12.9995,12.7691,12.7728,12.9958,12.4995,12.9958L0.499979,12.9958C0.226657,12.9958,0,12.7691,0,12.4958C0,12.2225,0.226657,11.9958,0.499979,11.9958L12.4995,11.9958Z',
        },
      },
    ],
  },
};

function EditOne(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={EditOneIcon} />;
}

EditOne.displayName = 'EchEditOneIcon';

export default EditOne;

import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/lib/components/AntdIcon';

const directionIcon: IconComponentProps['icon'] = {
  name: 'filterMenu',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { style: 'fill-rule:evenodd', viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M5.21167,2.5C6.0175,2.5,6.67,3.1525,6.67,3.95833L6.67,11.875C6.67,12.6804,6.01708,13.3333,5.21167,13.3333L1.45833,13.3333C0.652918,13.3333,-1.49012e-7,12.6804,0,11.875L0,3.95833C0,3.1525,0.653333,2.5,1.45833,2.5L5.21167,2.5ZM5.21167,3.75L1.45833,3.75C1.34327,3.75,1.25,3.84327,1.25,3.95833L1.25,11.875C1.25,11.99,1.34333,12.0833,1.45833,12.0833L5.21167,12.0833C5.32673,12.0833,5.42,11.9901,5.42,11.875L5.42,3.95833C5.42,3.84327,5.32673,3.75,5.21167,3.75ZM3.54333,10C3.8719,10.0001,4.1443,10.2546,4.16669,10.5824C4.18909,10.9102,3.95384,11.1994,3.62833,11.2442L3.54333,11.25L3.12667,11.25C2.7981,11.2499,2.5257,10.9954,2.50331,10.6676C2.48091,10.3398,2.71616,10.0506,3.04167,10.0058L3.12667,10L3.54333,10ZM14.7917,0C15.7775,-0.000121295,16.595,0.763164,16.6625,1.74667L16.6667,1.875L16.6667,8.95833C16.6668,9.94415,15.9035,10.7617,14.92,10.8292L14.7917,10.8333L7.5,10.8333L7.5,9.58333L14.7917,9.58333C15.1043,9.58374,15.3691,9.35307,15.4117,9.04333L15.4167,8.95833L15.4167,1.875C15.4167,1.56268,15.1861,1.29831,14.8767,1.25583L14.7917,1.25L5.20833,1.25C4.9435,1.25004,4.70744,1.41698,4.61917,1.66667L3.34417,1.66667C3.4445,0.769261,4.17024,0.0708455,5.07083,0.00499994L5.20833,0L14.7917,0ZM11.0417,7.5C11.3868,7.5,11.6667,7.77982,11.6667,8.125C11.6667,8.47018,11.3868,8.75,11.0417,8.75L8.95833,8.75C8.61316,8.75,8.33333,8.47018,8.33333,8.125C8.33333,7.77982,8.61316,7.5,8.95833,7.5L11.0417,7.5Z',
        },
      },
    ],
  },
};

function DirectionIcon(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={directionIcon} />;
}

DirectionIcon.displayName = 'EchDirectionIcon';

export default DirectionIcon;

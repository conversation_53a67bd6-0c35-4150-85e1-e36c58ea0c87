import { HTMLAttributes, MouseEventHandler } from 'react';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import classNames from 'classnames';
import EditOne from './svg/edit-one';
import Operate from './svg/operate';
import styles from './index.module.less';
import echosStyles from './echos.module.less';

export interface IconProps extends HTMLAttributes<HTMLElement> {
  name: string;
  size?: number | string;
  color?: string;

  onClick?: MouseEventHandler;
}

function Icon({
  name,
  size,
  color,
  className,
  style,

  ...others
}: IconProps) {
  const iStyle = {
    color,
    fontSize: typeof size === 'string' ? size : `${size}px`,
    ...style,
  };

  const isEchosStyle = name?.endsWith('_line');

  if (name === 'loading') {
    return <LoadingOutlined className={className} style={iStyle} {...others} />;
  }
  if (name === 'editOne') {
    return <EditOne className={className} style={iStyle} {...others} />;
  }
  if (name === 'operate') {
    return <Operate className={className} style={iStyle} {...others} />;
  }
  const classes = classNames(
    { [echosStyles['echos-iconfont']]: isEchosStyle },
    { [echosStyles[`icon-${name}`]]: isEchosStyle },
    styles.iconfont,
    styles[`icon-${name}`],
    className
  );
  return <i className={classes} style={iStyle} {...others} />;
}

Icon.defaultProps = {
  size: '',
  color: '',
  onClick: undefined,
};

export default Icon;

import { Layout } from '@echronos/antd';
import React, { PropsWithChildren } from 'react';
import Head, { ContextHeadTitle } from './head/head';
import './layout.less';

const { Header, Content } = Layout;

function Page({
  children,
  title,
}: PropsWithChildren<{
  title: ContextHeadTitle | ContextHeadTitle[];
}>) {
  return (
    <Layout className="micro-app__layout">
      <Header className="breadcrumb">
        <Head title={title} />
      </Header>
      <Content className="micro-app__main">{children}</Content>
    </Layout>
  );
}
export default Page;

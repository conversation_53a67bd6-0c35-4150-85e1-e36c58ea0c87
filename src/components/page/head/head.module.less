.flex-column() {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.root {
  padding-bottom: 16px;
}

.head {
  width: 100%;
  line-height: 32px;
  padding-bottom: 8px;
}

.back {
  line-height: 1;
  padding: 11px 10px 11px 0;
  border: 1px solid transparent;
  background-color: transparent;
  cursor: pointer;
}

.backIcon {
  color: #999eb2;

  &:hover {
    color: #00c6ff;
  }
}

.item {
  font-weight: 500;
}

.icon {
  font-size: 20px;
  font-weight: 600;
  margin-right: 12px;
  margin-left: 12px;
}

.textIcon {
  color: #888b98;
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
  margin-right: 12px;
  margin-left: 12px;
  position: relative;
  top: -1px;
  font-family: '苹方-简', sans-serif;
}

.item,
.icon {
  color: #888b98;
  vertical-align: top;
}

.list {
  .flex-column();

  font-size: 24px;
  display: inline-block;
  margin: 0;
  padding: 8px 32px 8px 0;
  list-style: none;
  white-space: nowrap;
  user-select: none;
  vertical-align: top;

  li {
    display: inline-block;
    vertical-align: top;

    &:last-child {
      .item {
        color: #040919;
      }
    }
  }
}

.linkItem {
  color: #888b98;
  cursor: pointer;

  &:hover {
    color: #29a6ff;
  }
}

.inner {
  .flex-column();
}

.quickFilter {
  color: #888b98;
  font-size: 18px;
  padding-top: 8px;
  padding-right: 12px;
  padding-bottom: 8px;
  white-space: nowrap;

  > span {
    color: #888b98;
  }

  :global {
    .ant-select-arrow {
      color: #888b98;
      right: 0;
    }

    .ant-select-selection-item {
      font-size: 18px;
    }
  }

  &:global {
    > .ant-select {
      font-size: inherit;

      > .ant-select-selector {
        padding-right: 0;
        padding-left: 4px;
      }
    }
  }

  .down {
    font-size: 14px;
    // color: #999eb2 !important;
  }
}

.toolbar {
  .flex-column();

  display: flex;
  height: 48px;
  padding-top: 8px;
  padding-bottom: 8px;
  justify-content: flex-end;
  flex-wrap: nowrap;
}

.hasQuickFilter {
  .list {
    flex-grow: 0;
  }

  .inner {
    .flex-column();
  }

  .toolbar {
    justify-content: flex-end;
  }

  :global {
    .ant-select-borderless .ant-select-arrow {
      color: #040919;
    }
  }
}

.tool {
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  margin-left: 12px;
  border-radius: 32px;
  text-align: center;
  white-space: nowrap;

  &:first-child {
    margin-left: 0;
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.button {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 1;
  padding: 5px 5px 4px;
  border: 1px solid transparent;
  border-radius: 10px;
  background-color: transparent;
  cursor: pointer;
  vertical-align: top;

  &:hover {
    color: #00c6ff;
    border-color: #fff;
    background-color: #fff;
  }

  .filterBtnIcon:hover {
    color: #00c6ff !important;
  }
}

// 返回按钮在小屏幕下，保持显示
// 在web端，导航大于一个时显示，等于一个时隐藏
// .screen-sm({
//   .back {
//     display: none;
//   }
// });

.webBack {
  display: inline-block;
}

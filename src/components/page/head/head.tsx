import { PropsWith<PERSON>hildren, ReactNode, useMemo } from 'react';
import { Row } from 'antd';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
// import { TooltipPlacement } from 'antd/es/tooltip';
import getEnv from '@/utils/env';
import { useBack } from '../../../hooks';
import TitleList, { ContextHeadTitle as ContextHeadTitleLink } from './title-list';
import Icon from '../../icon';
import styles from './head.module.less';

export type ContextHeadTitle = string | ContextHeadTitleLink;
export interface ToolbarType {
  iconName: string;
  tooltipTitle: string;
  active?: boolean;
  onClick: () => void;
}

export interface ContextHeadProps {
  title: ContextHeadTitle | ContextHeadTitle[];
  description?: string;
  keywords?: string;
  quickFilter?: ReactNode;
  isParent?: boolean;
  extra?: ReactNode;
  // toAdmin?: string | null;
  // placeholder?: string;
  // realTimeSearch?: boolean;
  // isFilterActive?: boolean;
  // searchTooltipPlacement?: TooltipPlacement;
  // searchTooltipTitle?: string;
  // toolbar?: ToolbarType[];
  // defaultSearchKey?: string;
  // onSearch?: MultipleParamsFn<[searchKey: string]>;
  // onFilter?: () => void;
  // onSearchClick?: () => void;
  otherBtn?: () => void;
}

function ContextHead({
  title,
  description,
  keywords,
  quickFilter,
  extra,
  isParent,
  children,
  otherBtn,
}: PropsWithChildren<ContextHeadProps>) {
  const [handleBack, pathBack, , location] = useBack();
  const headTitles = useMemo(() => {
    let tmp: ContextHeadTitle[];
    if (isArray(title)) {
      tmp = title;
      if (isParent && title.length > 1) {
        const index = title.findIndex((item) => !isString(item) && item.to === location.pathname);
        if (index !== -1) {
          tmp = title.slice(0, index + 1);
        }
      }
    } else {
      tmp = [title];
    }
    return tmp.map((item) => (isString(item) ? { title: item } : item));
  }, [title, isParent, location.pathname]);

  // headTitles大于1个时，倒数第二个title的to属性，即为返回上一级的to属性
  const toPath = useMemo(
    () =>
      headTitles.length > 1 ? headTitles[headTitles.length - 2].to : getEnv('BIZ_APP_HOME_URL'),
    [headTitles]
  );

  const classes = classNames(styles.head, { [styles.hasQuickFilter]: quickFilter });
  return (
    <>
      <Helmet>
        {headTitles.length ? (
          <title>
            {headTitles[headTitles.length - 1].title} · {getEnv('BIZ_APP_TITLE') || ''}
          </title>
        ) : null}
        {description ? <meta name="description" content={description} /> : null}
        {keywords ? <meta name="keywords" content={keywords} /> : null}
      </Helmet>
      <Row align="top" className={classes}>
        <div style={{ whiteSpace: 'nowrap' }}>
          <button
            type="button"
            className={classNames(styles.back, { [styles.webBack]: headTitles.length > 1 })}
            onClick={() => (toPath ? pathBack(toPath) : handleBack)}
          >
            <Icon name="left" size={24} className={styles.backIcon} />
          </button>
          <TitleList titles={headTitles} />
        </div>
        <div className={styles.inner}>
          {quickFilter}
          <div className={styles.toolbar}>
            {/* {onSearch ? (
              <SearchButton
                tooltipPlacement={searchTooltipPlacement}
                tooltipTitle={searchTooltipTitle}
                placeholder={placeholder}
                defaultValue={defaultSearchKey}
                onSearch={onSearch}
                realTimeSearch={realTimeSearch}
                onClick={onSearchClick}
              />
            ) : null} */}
            {otherBtn ? (
              <button
                type="button"
                className={styles.button}
                style={{ marginLeft: '12px' }}
                onClick={otherBtn}
              >
                <Icon name="reset" size={20} color="#040919" />
              </button>
            ) : null}
            {extra}
          </div>
        </div>
      </Row>
      {children}
    </>
  );
}

ContextHead.displayName = 'ContextHead';

ContextHead.defaultProps = {
  description: '',
  keywords: '',
  quickFilter: null,
  extra: null,
  isParent: false,
  // toAdmin: null,
  // placeholder: '搜索',
  // searchTooltipPlacement: 'bottom' as TooltipPlacement,
  // searchTooltipTitle: '',
  // realTimeSearch: true,
  // isFilterActive: false,
  // toolbar: [],
  // defaultSearchKey: '',
  // onSearch: undefined,
  // onFilter: undefined,
  // onSearchClick: undefined,
  otherBtn: undefined,
};

export default ContextHead;

import { useEffect, useMemo, useState } from 'react';
import { To } from 'react-router-dom';
import { useNavigate } from '@/hooks';
import type { NavigateOptions } from 'react-router/lib/hooks';
import { Dropdown, Menu } from 'antd';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import Icon from '../../icon';
import styles from './head.module.less';

export interface ContextHeadTitle {
  title: string;
  to?: To;
  opt?: NavigateOptions;
  toBefore?: () => Promise<boolean>;
}

export interface TitleListProps {
  titles: ContextHeadTitle[];
}

function TitleList({ titles }: TitleListProps) {
  const navigate = useNavigate();
  const [width, setWidth] = useState(0);
  const children = useMemo(() => {
    const getTitleItem = (item: ContextHeadTitle) => {
      if (item.to) {
        return (
          <span
            className={classNames(styles.linkItem, styles.item)}
            role="button"
            tabIndex={0}
            onClick={() => {
              if (item.toBefore) {
                item.toBefore().then((flag) => {
                  if (flag && item && item.to) {
                    navigate(item.to, item.opt);
                  }
                });
              } else {
                navigate(item.to, item.opt);
              }
            }}
          >
            {item.title}
          </span>
        );
      }

      return <span className={styles.item}>{item.title}</span>;
    };

    if (titles.length !== 0) {
      const count = width < 1660 ? 3 : 4;
      if (titles.length <= count) {
        return titles.map((title, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <li key={index}>
            {/* {!!index && <Icon name="right" size={20} className={styles.icon} />} */}
            {!!index && <span className={styles.textIcon}>/</span>}
            {getTitleItem(title)}
          </li>
        ));
      }
      const items = [<li key={0}>{getTitleItem(titles[0])}</li>];
      const menuMaxIndex = titles.length - (count - 2);
      const menuItems = titles
        .slice(1, menuMaxIndex)
        .map((title, index) => ({ key: index + 1, label: title.title }));
      const menu = (
        <Menu
          items={menuItems}
          style={{ width: '162px' }}
          onClick={(info) => {
            const item = titles[+info.key];
            if (item && item.to) {
              console.log(1234);
              navigate(item.to, item.opt);
            }
          }}
        />
      );
      items.push(
        <li key={1}>
          {/* <Icon name="right" size={20} className={styles.icon} /> */}
          <span className={styles.textIcon}>/</span>
          <Dropdown overlay={menu}>
            <Icon name="zu13366" size={20} className={styles.item} />
          </Dropdown>
        </li>
      );
      titles.slice(menuMaxIndex).forEach((title) => {
        items.push(
          <li key={items.length}>
            {/* <Icon name="right" size={20} className={styles.icon} /> */}
            <span className={styles.textIcon}>/</span>
            {getTitleItem(title)}
          </li>
        );
      });
      return items;
    }
    return null;
  }, [titles, width]); // eslint-disable-line

  useEffect(() => {
    const resize = debounce(() => {
      setWidth(window.innerWidth);
    }, 100);
    resize();
    window.addEventListener('resize', resize, false);
    return () => {
      window.removeEventListener('resize', resize, false);
    };
  }, []);

  return <ul className={styles.list}>{children}</ul>;
}

TitleList.displayName = 'HeadTitleList';

export default TitleList;

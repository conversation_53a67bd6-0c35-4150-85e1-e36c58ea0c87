import request from './base';

export interface ObsTokenResult {
  access: string;
  bucket: string;
  expiresAt: number;
  filePath: string;
  obsHost: string;
  secret: string;
  token: string;
}

const minute: number = 60 * 1000;

function getObsToken(): Promise<ObsTokenResult> {
  return request('/v1/imc/file/upload/token').then(({ credential }) => {
    let expiresAt: number;
    // 将格式化时间转换为字符串
    // 为了保险起见，将临时令牌的过期时间向推一分钟
    try {
      expiresAt = new Date(+credential.expires_at).getTime() - minute;
    } catch (e) {
      // 因为华为云文档中临时令牌使用时限最短为 15 分钟
      expiresAt = new Date().getTime() + 14 * minute;
    }
    return {
      ...credential,
      expiresAt,
      token: credential.securitytoken,
    };
  });
}

export default getObsToken;

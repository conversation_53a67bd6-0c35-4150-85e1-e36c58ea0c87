import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface TopicAnnouncementParams {
  tenantId: string;
  topicIdSet?: number[];
  topicTypeList: number[];
  keyword?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface TopicAnnouncementType {
  id: number;
  topicName: string;
  topicHead: string;
  managerName: string;
  createTime: number;
}
/**
 * 公告装修 社区列表 查询(分页)
 */
function getTopicAnnouncementList(
  params: TopicAnnouncementParams
): PaginationResponse<TopicAnnouncementType> {
  return request('/v1/cms/topic/announcement/list', {
    method: 'GET',
    params,
  });
}

export default getTopicAnnouncementList;

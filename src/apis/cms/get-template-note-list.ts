import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface ParamsType {
  topicName: string;
  searchKey?: string;
  pageSize?: number;
  pageNo?: number;
}

export interface GetTemplateNoteListType {
  id: number;
  type: number;
  title: string;
  coverImageList: string[];
  publisherId: number;
  publisherName: string;
  publisherLogo: string;
  likeNum: number;
  isLike: boolean;
  content: string;
  publishTime: string;
}
/**
 * 模板广场笔记列表
 */
function getTemplateNoteList(params: ParamsType): PaginationResponse<GetTemplateNoteListType> {
  return request('/v1/note/temp/community/page/list', {
    method: 'GET',
    params,
  });
}

export default getTemplateNoteList;

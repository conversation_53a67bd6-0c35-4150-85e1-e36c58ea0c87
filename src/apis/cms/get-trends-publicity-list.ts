import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface TrendsPublicityParams {
  tenantId: string;
  moduleType?: number; // 模块类型
  moduleTypes?: number[];
  topicId?: number; // 社区id
  topicIds?: number[];
  keyWord?: string;
  pageNo?: number;
  pageSize?: number;
}

interface PictureVideo {
  goods: null;
  goodsId: number;
  goodsInfo: null;
  height: number;
  link: null;
  picUrl: string;
  type: number;
  videoTime: number;
  videoUrl: string;
  width: number;
}

interface TopicList {
  adminAmount: number;
  auditStatus: number;
  autoJoin: number;
  background: string;
  bid: number;
  canChat: number;
  canSearch: number;
  category: number;
  code: string;
  companyId: number;
  companyName: string;
  createTime: number;
  createUser: number;
  departmentId: number;
  genre: number;
  genreId: number;
  glanceNum: number;
  groupChat: number;
  groupChatSession: number;
  hotScore: number;
  id: number;
  isAdmin: number;
  isCheck: number;
  isConcernTopic: number;
  isHotTrends: number;
  isMyTopic: number;
  isRecentTopic: number;
  isTopicMain: number;
  issue: number;
  joinCheck: number;
  memberAmount: number;
  pageId: string;
  source: number;
  status: number;
  topicForwardingNumber: number;
  topicHead: string;
  topicIntro: string;
  topicLabel: number;
  topicMain: string;
  topicMainAvatar: string;
  topicMainId: number;
  topicName: string;
  topicPermissionVOS: [];
  topicType: number;
  trendsAmount: number;
  type: number;
  updateTime: number;
  updateUser: number;
  userInfoVO: null;
}

interface TrendsTopic {
  id: number;
  location: number;
  topicHead: string;
  topicName: string;
}

export interface TrendsPublicityType {
  article: string;
  auditStatus: number;
  avatar: string;
  commentNumber: number;
  content: string;
  countHelp: number;
  createTime: number;
  createUser: number;
  file: [];
  forwardingNumber: number;
  friendUserList: [];
  glanceNum: number;
  goods: [];
  hotCommentsNumber: number;
  hotScore: number;
  id: number;
  isBrowse: number;
  isConcernUser: number;
  isPraise: number;
  isRecommend: number;
  isTop: number;
  layersPrice: [];
  link: [];
  location: null;
  manualHotType: number;
  moduleType: number;
  nickname: string;
  operationMemberId: number;
  pictureVideo: PictureVideo[];
  praiseId: number;
  privacyType: number;
  pushingId: number;
  releaseNumber: number;
  remark: string;
  shop: [];
  source: number;
  sumCountHelp: number;
  surplusCountHelp: number;
  territory: string;
  thumbUpNumber: number;
  title: string;
  topicList: TopicList[];
  trendsSource: number;
  trendsTopic: TrendsTopic[];
  updateTime: number;
  updateUser: number;
  userInfoVO: {
    account: string;
    avatar: string;
    id: number;
    nickname: string;
    remark: string;
  };
  visitingCard: [];
}
/**
 * 分页查询公开的（文章、动态、公告）列表
 */
function getTrendsPublicityList(
  data: TrendsPublicityParams
): PaginationResponse<TrendsPublicityType> {
  return request('/v1/cms/trends/publicity/page/list', {
    method: 'POST',
    data,
  });
}

export default getTrendsPublicityList;

import http from './http';

interface PublishNoteParamsType {
  type: number; // 类型：1图片 2视频 3文章
  isEnterprise: number; // 是否企业名义：1是 0否
  title: string; // 标题
  isIntelligent: number; // 是否智能名义：1是 0否
  publisherId: number; // 发布者ID 用户ID/公司ID
  imageList?: string[]; // 图片列表
  coverImageList?: string[] | number; // 封面图片列表
  jsonContent: string;
  content: string;
  video?: string; // 视频地址
  topicIds?: number[]; // 话题ID列表
  tenantId?: number; // 租户ID
  coverType?: number; // 封面类型：	封面类型：1内容图片 2单独上传 3无封面
  productType?: number;
  productIds?: string[]; // 放模板id
}

// 管理台发布笔记
function publishManageNotes(data: PublishNoteParamsType) {
  return http('/v1/note/manage/publish', {
    method: 'POST',
    data,
  });
}

export default publishManageNotes;

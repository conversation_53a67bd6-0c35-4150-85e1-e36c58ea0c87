import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface BiddingTrendsParams {
  siteId: number;
  categoryIdList?: number[];
  trendsTypeList?: number[];
  pageNo?: number;
  pageSize?: number;
}

interface CategoryList {
  id: number;
  level: number;
  name: string;
}

interface PictureVideoList {
  goodsId: null;
  height: null;
  picUrl: string;
  type: number;
  videoTime: null;
  videoUrl: string;
  width: null;
}

export interface BiddingTrendsListType {
  categoryList: CategoryList[];
  companyId: number;
  companyName: string;
  endTime: number;
  id: number;
  issuedTime: number;
  mainSheetId: number;
  pictureVideoList: PictureVideoList[];
  status: number;
  title: string;
}
/**
 * 根据站点ID分页获取采购公示信息
 */
function getBiddingTrendsSiteMainSheetList(
  data: BiddingTrendsParams
): PaginationResponse<BiddingTrendsListType> {
  return request('/v1/bidding/trendsSite/pageMainSheet', {
    method: 'POST',
    data,
  });
}

export default getBiddingTrendsSiteMainSheetList;

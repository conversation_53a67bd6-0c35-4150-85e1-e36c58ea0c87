import { PaginationResponse, createHttpRequest } from '@echronos/core';

const request = createHttpRequest('ech-iform');

export interface WorkOrderTemplate {
  id: number; // 主键
  name: string; // 表单名称
  formCode: string; // 表单编码
  isEnable: number; // 是否启用
  createTime: string; // 创建时间
  updateTime: string; // 修改时间
}

/**
 * 获取工单模板列表
 */
function getWorkOrderTemplates(
  params: { name?: string } = {}
): PaginationResponse<WorkOrderTemplate> {
  return request('/v1/form/edesk', { params: { type: 4, ...params } });
}

export default getWorkOrderTemplates;

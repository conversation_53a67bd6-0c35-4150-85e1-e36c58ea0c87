import request from './base';

interface ParamsType {
  spaceId: string;
}

export interface TreeChildrenType {
  attrs: {
    isHomePage: boolean;
    pageName: string;
  };
  blockId: string;
  rootBlockId: string;
  children: TreeChildrenType[];
}

export interface GetSpacePageResultType {
  spaceId: string;
  name: string;
  logo: string;
  id: string;
  description: string;
  children: TreeChildrenType[];
}

// 查询单个空间及空间下的所有子页面
function getSpacePageList(params: ParamsType): Promise<GetSpacePageResultType> {
  return request('/v1/space/list/single', { params, method: 'GET' });
}

export default getSpacePageList;

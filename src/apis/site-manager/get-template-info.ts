import request from './base';

interface ParamsType {
  id: string;
}

export interface TemplateInfoResultType {
  id: string;
  spaceId: string;
  name: string;
  topicList: {
    id: number;
    name: string;
  }[];
  coverImage: string;
  saleProductId: string;
  marketPrice: number;
  appList: {
    id: number;
    name: string;
    appIcon: string;
  }[];
}

// 发布笔记查询模板信息
function getTemplateInfo(params: ParamsType): Promise<TemplateInfoResultType> {
  return request('/v1/temp/get/info/to/note', { params, method: 'GET' });
}

export default getTemplateInfo;

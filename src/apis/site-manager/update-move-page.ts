import http from './base';

export interface ParamsType {
  blockId: string;
  targetParentBlockId: string;
  sourceParentBlockId: string;
  targetParentContent: string[];
  sourceParentType: string;
  targeParentType: string;
  tenantId: string;
}

// 移动页面
function updateMovePage(data: ParamsType) {
  return http('/v1/site/move', {
    method: 'POST',
    data,
  });
}

export default updateMovePage;

import http from './base';

export interface CreateSubmitTemplateParams {
  name: string;
  indexBlock: string;
  tempCategories: string[];
  tempPrice: number;
  blockIds: string[];
  appInfos: {
    appId: number;
    productId: number;
    productType: number;
  }[];
  coverImage: string;
}

// 提交模板
function createSubmitTemplate(data: CreateSubmitTemplateParams) {
  return http('/v1/temp/submit', {
    method: 'POST',
    data,
    timeout: 60000,
  });
}

export default createSubmitTemplate;

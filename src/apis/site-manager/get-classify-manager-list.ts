import { PaginationResponse } from '@echronos/core';
import request from './base';

export interface GetClassifyManagerListType {
  id: string;
  name: string;
  describe: string;
  isShow: number;
  type: number;
}

// 查询分类管理列表
function getClassifyManagerList(): PaginationResponse<GetClassifyManagerListType> {
  return request('/v1/temp/classify/manager/list', { method: 'GET' });
}

export default getClassifyManagerList;

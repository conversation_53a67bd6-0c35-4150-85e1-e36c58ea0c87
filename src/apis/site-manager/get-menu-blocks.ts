import { PaginationResponse, createHttpRequest } from '@echronos/core';

export interface Block {
  id: number;
  appId: string;
  blockName: string; // 组件名称;
  export: string; // 组件类型;
  blockGroup: string; // 组件储存在文档中的类型
  brief: string; // 组件描述;
  backgroundImage: string; // 组件图标;
}

export interface MenuBlock {
  appId: string;
  appName: string; // 应用名称
  description: string; // 应用描述;
  iconUrl: string; // 应用图标
  blockList: Block[];
}

export interface MenuList {
  name: string;
  includesApps?: string[];
  includesBlocks: string[];
  children: Block[];
}

interface GetMenuBlocksParams {
  /** 能力唯一code */
  abilityCode: string;
  /** 信息块code */
  exportList: string;
}

/**
 * 查询信息块
 */
function getMenuBlocks(params: GetMenuBlocksParams): PaginationResponse<MenuBlock> {
  return createHttpRequest('ech-acm')('/v1/app/listForAbilityCode', {
    params,
    method: 'GET',
  });
}

export default getMenuBlocks;

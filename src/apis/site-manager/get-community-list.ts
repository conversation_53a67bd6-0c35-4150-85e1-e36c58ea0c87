import { PaginationResponse, createHttpRequest } from '@echronos/core';

const request = createHttpRequest('ech-cms');

export interface GetCommunityListParams {
  tenantId?: string; // 租户ID
  keyword?: string; // 关键字搜索
  pageNo?: number; // 页码
  pageSize?: number; // 大小
  topicIdSet?: number[]; // 社区id
}

export interface Community {
  id: number; // 主键ID
  topicName: string; // 社区名称
  topicHead: string; // 社区头像
  managerName: string; // 社长昵称
  createTime: string; // 创建时间
}

/**
 * 查询社区列表
 */
function getCommunityList(params: GetCommunityListParams): PaginationResponse<Community> {
  return request('/v1/cms/topic/site/list', { method: 'GET', params });
}

export default getCommunityList;

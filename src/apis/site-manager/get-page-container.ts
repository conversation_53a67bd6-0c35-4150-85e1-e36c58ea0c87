import { PaginationResponse } from '@echronos/core';
import request from './base';

export interface ContainerTemplate {
  id: string;
  name: string; // 组件名称
  type: string; // 组件类型
  description: string; // 组件描述
  icon: string; // 图标
  classify: string; // 前端要的内部类别字段
  parentId: string; // 父ID
  sort: number; // 排序
  blockId: string; // 块id/容器id
  isDefaultContainer: boolean;
}

interface getPageContainerParams {
  type: string;
  tenantId: string;
}

/**
 * 获取页面容器
 */
function getPageContainer(params: getPageContainerParams): PaginationResponse<ContainerTemplate> {
  return request('/v1/site/container/components', {
    params,
    method: 'GET',
  });
}

export default getPageContainer;

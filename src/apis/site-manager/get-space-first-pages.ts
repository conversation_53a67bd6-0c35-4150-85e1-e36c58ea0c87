import request from './base';
import { PaginationResponse } from '../utils/utils';

export interface ParamsType {
  spaceId: string;
  tenantId: string;
}

export interface GetSpaceFirstPageListsType {
  blockId: string;
  spaceId: string;
  type: string; // 类型(blockType:1.space-空间 2.block-块 3.page)
  attrs: {
    pageName: string;
    logo: string;
  };
  logo: string;
  name: string;
  key: string;
}

/**
 * 查询当前登录用户某个空间下有权看到的一级页面
 */
function getSpaceFirstPages(params: ParamsType): PaginationResponse<GetSpaceFirstPageListsType> {
  return request('/v1/site/get/first/page', { method: 'GET', params });
}

export default getSpaceFirstPages;

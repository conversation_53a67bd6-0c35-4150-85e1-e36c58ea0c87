import request from './base';

interface UpdateBlockInfo {
  blockId: string; // 块ID
  attrs: Record<string, any>;
  type: string; // 组件类型
  content: string[];
  parentId: string; // 父节点ID
  refBlockId?: string;
  // spaceId: string; // 空间id
}

export interface UpdatePageBlockParams {
  blockId: string; // 块ID
  spaceId: string; // 空间id
  attrs?: Record<string, any>;
  rootBlockId: string; // 根页面块ID
  type: string; // 类型(blockType:paragraph,heading1,heading2,heading3,heading4,heading5,heading6,foldNav,image,carousel,hotZone,specifyDynamics,sortDynamics,levitateNav)
  content: string[];
  block?: UpdateBlockInfo[];
  refBlockId?: string;
  tenantId: string;
}

/**
 * 修改页面block
 */
function updatePageBlock(params: UpdatePageBlockParams) {
  return request('/v1/site/page/block', { data: params, method: 'POST' });
}

export default updatePageBlock;

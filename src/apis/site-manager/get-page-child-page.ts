import http from './base';
import { PaginationResponse } from '../utils/utils';
import { GetSpaceFirstPageListsType } from './get-space-first-pages';

export interface ParamsType {
  blockId: string; // 块ID(父页面blockId，和父节点ID保持一致即可)
  tenantId: string;
}

// 查询当前登录用户某个空间下有权看到的一级页面
function getPageChildPages(params: ParamsType): PaginationResponse<GetSpaceFirstPageListsType> {
  return http('/v1/site/get/child/page', {
    method: 'GET',
    params,
  });
}

export default getPageChildPages;

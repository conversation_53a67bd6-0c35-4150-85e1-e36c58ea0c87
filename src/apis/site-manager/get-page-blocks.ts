import request from './base';

export interface IBlock {
  blockId: string; // 块ID
  attrs: Record<string, any>;
  type: string; // 组件类型
  content: string[];
  parentId: string; // 父节点ID
  spaceId: string; // 空间id
  createUser: number; // 创建人
  createTime: string; // 创建时间
  updateUser: number; // 更新人
  updateTime: string; // 更新时间
  publishStatus?: number; // 发布状态
}

export interface PageBlockDetail extends IBlock {
  block: Record<string, IBlock>;
}

interface getPageBlocksParams {
  blockId: string;
  tenantId: string;
}

/**
 * 装修-空间-查询页面详情
 */
function getPageBlocks(params: getPageBlocksParams): Promise<PageBlockDetail> {
  return request('/v1/site/page/blocks', {
    params,
    method: 'GET',
  });
}

export default getPageBlocks;

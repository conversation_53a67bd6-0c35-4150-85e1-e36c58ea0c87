import { PaginationResponse } from '@echronos/core';
import request from './base';

export interface IMenuBlock {
  id: string;
  name: string; // 组件名称;
  type: string; // 组件类型;
  description: string; // 组件描述;
  icon: string; // 图标;
  classify: string; // 前端要的内部类别字段;
  parentId: string; // 父ID;
  sort: number;
  children: IMenuBlock[];
}

/**
 * 查询菜单组件列表
 */
function getSiteMenuBlocks(): PaginationResponse<IMenuBlock> {
  return request('/v1/site/components', {
    params: {},
    method: 'GET',
  });
}

export default getSiteMenuBlocks;

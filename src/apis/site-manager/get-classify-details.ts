import request from './base';

interface ParamsType {
  id: string;
}

interface GetCommunityListType {
  id: string;
  name: string;
  style: number;
  label: string;
  image: string;
  backgroundImage: string;
  describe: string;
}

// 分类详情
function getClassifyDetails(params: ParamsType): Promise<GetCommunityListType> {
  return request('/v1/temp/classify/detail', { params, method: 'GET' });
}

export default getClassifyDetails;

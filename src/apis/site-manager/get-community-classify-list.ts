import { PaginationResponse } from '@echronos/core';
import request from './base';

export interface CommunityClassifyListType {
  id: string;
  name: string;
  style: number;
  label: string;
  image: string;
  backgroundImage: string;
  describe: string;
  isShow: number;
  type: number;
}

// 模板广场分类列表
function getCommunityClassifyList(): PaginationResponse<CommunityClassifyListType> {
  return request('/v1/temp/classify/community/list', { method: 'GET' });
}

export default getCommunityClassifyList;

import { PaginationResponse } from '@echronos/core';
import { TemplateResultType } from './get-template-buy-list';
import request from './base';

interface ParamsType {
  pageNo?: number;
  pageSize?: number;
}

// 创建的模板
function getTemplateCreateList(params: ParamsType): PaginationResponse<TemplateResultType> {
  return request('/v1/temp/create/list', { params, method: 'GET' });
}

export default getTemplateCreateList;

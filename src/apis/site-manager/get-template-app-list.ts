import request from './base';
import { PaginationResponse } from '../utils/utils';

export interface ParamsType {
  blockIds: string[];
}

export interface GoodsListType {
  id: number;
  name: string;
  marketPrice: number;
}

export interface GetTemplateAppResultType {
  id: number;
  name: string;
  appIcon: string;
  productId?: number;
  price?: number;
  isSelect?: boolean;
  goodsList: GoodsListType[];
}

/**
 * 模板应用列表
 */
function getTemplateAppList(data: ParamsType): PaginationResponse<GetTemplateAppResultType> {
  return request('/v1/temp/app/list', { method: 'POST', data, timeout: 60000 });
}

export default getTemplateAppList;

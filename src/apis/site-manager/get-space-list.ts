import request from './base';
import { PaginationResponse } from '../utils/utils';

interface ParamsType {
  tenantId: string;
}

export interface SpaceListType {
  id: string;
  spaceId: string;
  name: string;
  logo: string;
  description: string;
  perCodes: string[];
  isOwner: boolean;
  createTime: string;
  content: string[];
  type: string;
  hasChild: boolean;
  roleIds: string[];
  key: string;
  blockId: string;
  children: SpaceListType[];
  spaceName: string;
  spaceLogo: string;
  pageList: SpaceListType[];
  ownerId: number;
  parentId: string;
  attrs: {
    pageName: string;
    logo: string;
  };
}

// 获取建站空间列表
function getSpaceList(params: ParamsType): PaginationResponse<SpaceListType> {
  return request('/v1/space/list', { method: 'GET', params });
}

export default getSpaceList;

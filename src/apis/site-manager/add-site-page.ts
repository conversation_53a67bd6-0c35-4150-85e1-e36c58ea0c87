import request from './base';

export interface AddSitePageParams {
  spaceId: string; // 空间id
  attrs: Record<string, any>;
  parentId: string; // 父节点ID
  blockId: string; // 块ID
  rootBlockId: string; // 根页面块ID
  content: string[];
  tenantId: string;
}

/**
 * 新增站点页面
 */
function addSitePage(params: AddSitePageParams) {
  return request('/v1/site/page', { data: params, method: 'POST' });
}

export default addSitePage;

import { PaginationResponse } from '@echronos/core';
import request from './base';

interface ParamsType {
  searchKey?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface GetTemplatePageResultType {
  id: number;
  name: string;
  tempPrice: number;
  isShield: number;
  releaseTime: string;
  status: number;
  publisher: number;
  publisherName: number;
  publisherType: number;
  appPrice: number;
  totalPrice: number;
  tempClassVOS: {
    id: string;
    name: string;
  }[];
  indexId: string;
}

// 运营管理模板列表
function getTemplatePageList(params: ParamsType): PaginationResponse<GetTemplatePageResultType> {
  return request('/v1/temp/manager/page/list', { params, method: 'GET' });
}

export default getTemplatePageList;

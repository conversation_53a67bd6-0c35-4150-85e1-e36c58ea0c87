import request from './base';

interface SpaceDetailsParams {
  spaceId: string;
  tenantId: string;
}

export interface GetSpaceDetailsData {
  spaceId: string;
  name: string;
  logo: string;
  description: string;
  blockId: string;
  isOwner: boolean;
  perCodes: string[];
  ownerId: number;
  key: string;
}

// 查看空间详情
function getSpaceDetails(params: SpaceDetailsParams): Promise<GetSpaceDetailsData> {
  return request('/v1/space/detail', { method: 'GET', params, autoToast: false });
}

export default getSpaceDetails;

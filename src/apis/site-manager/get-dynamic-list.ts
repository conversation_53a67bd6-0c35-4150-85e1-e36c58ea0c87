import { PaginationResponse, createHttpRequest } from '@echronos/core';

const request = createHttpRequest('ech-cms');

export interface GetDynamicListParams {
  topicIdSet?: number[]; // 社区ID集合
  keyword?: string; // 关键字搜索
  pageNo?: number; // 页码
  pageSize?: number; // 大小
  userIdSet?: number[]; // 动态发布人集合
  trendsIdSet?: number[]; // 动态ID集合
  source?: 0 | 1;
  sortType: number; // 0 | 1;
  tenantId: string; // 商户id
}

export interface Dynamic {
  id: number; // 主键ID
  topicName: string; // 社区名称
  topicHead: string; // 社区头像
  content: string; // 文章内容
  createTime: string; // 发布时间
  createName: string; // 发布人名称
  createAvatar: string; // 创建人头像

  pictureVideo: {
    type: 0 | 1;
    picUrl: string;
    videoUrl: string;
  }[];
}

/**
 * 查询动态列表
 */
function getDynamicList(params: GetDynamicListParams): PaginationResponse<Dynamic> {
  return request('/v1/cms/trends/site/list', { method: 'GET', params });
}

export default getDynamicList;

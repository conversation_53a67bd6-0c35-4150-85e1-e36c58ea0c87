import http from './base';
import { SpaceListType } from './get-space-list';

export interface SearchPageParamsType {
  keyWord: string;
  tenantId: string;
}

export interface GetSearchPageListType {
  searSpaceList: SpaceListType[];
  searShareSpace: {
    pageList: SpaceListType[];
  };
}

function getSearchPage(params: SearchPageParamsType): Promise<GetSearchPageListType> {
  return http('/v1/site/search', { method: 'GET', params });
}

export default getSearchPage;

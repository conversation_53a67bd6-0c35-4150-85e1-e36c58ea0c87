import { PaginationResponse } from '@echronos/core';
import request from './base';

interface ParamsType {
  pageNo?: number;
  pageSize?: number;
}

export interface TemplateResultType {
  id: string;
  name: string;
  isShield: number;
  status: number;
  ownerId: number;
  designerName: string;
  indexId: string;
  coverImage: string;
  appList: {
    id: number;
    name: string;
    appIcon: string;
  }[];
}

// 已购买的模板
function getTemplateBuyList(params: ParamsType): PaginationResponse<TemplateResultType> {
  return request('/v1/temp/buy/list', { params, method: 'GET' });
}

export default getTemplateBuyList;

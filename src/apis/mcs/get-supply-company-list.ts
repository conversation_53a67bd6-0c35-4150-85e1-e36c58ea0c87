import request from './http';
import { Paginate, PaginationResponse } from '../utils/utils';

export interface GetMcsShopOperatorSourceProps extends Paginate {
  tenantId: string;
  type: number;
}

export type GetMcsShopOperatorSourceRes = {
  id: number;
  shopName: string;
  companyName: string;
};

function getMcsShopOperatorSource(
  data: GetMcsShopOperatorSourceProps
): PaginationResponse<GetMcsShopOperatorSourceRes> {
  return request('/v1/mall/supply/company/list', { method: 'GET', params: data });
}

export default getMcsShopOperatorSource;

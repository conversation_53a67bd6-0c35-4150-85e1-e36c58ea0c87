import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface SupplyPackageSkuDecorationListParams {
  tenantId: string;
  packageIds?: number[];
  orderByType?: number;
  customizeCategoryCode?: string;
  companyIds?: number[];
  ignoreCompanyIds?: number[];
  searchKey?: string;
  pageSize?: number;
  pageNo?: number;
}

export interface ShopPackageListType {
  companyId: number;
  companyName: string;
  detail: string;
  groupDetailImageList: string[];
  hasLink: boolean;
  id: number;
  imageList: string[];
  introduce: string;
  marketPrice: string;
  name: string;
  owner: true;
  price: string;
  shopId: number;
  shopLogo: string;
  shopName: string;
  skuList: {
    groupId: number;
    image: string;
    isGift: number;
    payWayNo: number;
    productId: number;
    quantity: number;
    saleGroup: number;
    shopId: number;
    shopSkuId: number;
    skuId: number;
    unit: string;
  }[];
  upTime: number;
}

/**
 * 站点装修查询可选择商品包列表
 */
function getSupplyPackageSkuDecorationList(
  params: SupplyPackageSkuDecorationListParams
): PaginationResponse<ShopPackageListType> {
  return request('/v1/mall/supply/package/decoration/page/list', {
    data: params,
    method: 'POST',
  });
}

export default getSupplyPackageSkuDecorationList;

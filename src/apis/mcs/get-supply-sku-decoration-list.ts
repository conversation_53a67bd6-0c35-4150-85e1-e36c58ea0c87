import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface SupplySkuDecorationListParams {
  tenantId: string;
  shopSkuIds?: number[];
  randomCount?: number;
  orderByType?: number;
  customizeCategoryCode?: string;
  mallCatLabelCode?: string;
  companyIds?: number[];
  ignoreCompanyIds?: number[];
  searchKey?: string;
  pageSize?: number;
  pageNo?: number;
}

export interface SupplySkuDecorationListType {
  brand: string;
  categoryId: number;
  categoryName: string;
  companyId: number;
  companyName: string;
  currentPayWayNo: number;
  extendMapJson: string;
  hasLink: boolean;
  id: number;
  images: string[];
  marketPrice: number;
  marketPriceStr: string;
  minimum: number;
  name: string;
  price: number;
  priceStr: string;
  saleGroup: number;
  saleGroupStr: string;
  shopId: number;
  shopLogo: string;
  shopName: string;
  skuId: number;
  standardList: { name: string; value: string }[];
  unit: string;
  validStatus: number;
}

/**
 * 站点装修查询可选择商品列表
 */
function getSupplySkuDecorationList(
  params: SupplySkuDecorationListParams
): PaginationResponse<SupplySkuDecorationListType> {
  return request('/v1/mall/supply/sku/decoration/page/list', {
    data: params,
    method: 'POST',
  });
}

export default getSupplySkuDecorationList;

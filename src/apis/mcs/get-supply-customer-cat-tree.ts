import request from './http';
import { PaginationResponse } from '../utils/utils';
import { GetMcsShopOperatorSourceRes } from './get-supply-company-list';

export interface SupplyCustomerCatTreeParams {
  tenantId: string;
  countType: number;
  companyId?: number;
  ignoreCompanyId?: number;
}

interface ChildrenList {
  childrenList: ChildrenList[];
  code: string;
  id: number;
  image: string;
  name: string;
  parentName: string;
  shopSkuCount: number;
  sort: string;
}

export interface SupplyCustomerCatTreeType {
  childrenList: ChildrenList[];
  code: string;
  id: number;
  image: string;
  name: string;
  parentName: string;
  shopSkuCount: number;
  sort: string;
  isShow?: boolean; // 前端定义使用
  companyIds?: number[];
  companyItemList?: GetMcsShopOperatorSourceRes[];
}

/**
 * 站点查询自定义分类并且统计站点商品数量
 */
function getSupplyCustomerCatTree(
  params: SupplyCustomerCatTreeParams
): PaginationResponse<SupplyCustomerCatTreeType> {
  return request('/v1/mall/supply/customer/cat/tree/count', {
    params,
    method: 'GET',
  });
}

export default getSupplyCustomerCatTree;

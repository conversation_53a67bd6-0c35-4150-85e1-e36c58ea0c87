import request from './http';

export interface GetSeoByBlockResponse {
  id?: number;
  browserTitle: string; // 浏览器标题
  browserIcon: string; // 浏览器图标
  seoKeyword: string; // 网站SEO关键词
  seoDesc: string; // 网站SEO描述
  tenantId: string;
  domain?: string;
}

export default function getSeoByBlock(
  blockId: string,
  tenantId: string
): Promise<GetSeoByBlockResponse> {
  return request(`/v1/seo/query/tenant/block/seo`, {
    method: 'GET',
    params: { blockId, tenantId },
  });
}

import request from './http';

export interface UpdateByBlockRequest {
  id?: number;
  blockId?: string; // 页面blockId
  browserTitle?: string;
  browserIcon?: string;
  seoKeyword?: string;
  seoDesc?: string;
  tenantId: string;
}

export default function updateSeoByBlock(data: UpdateByBlockRequest) {
  return request(`/v1/seo/update/tenant/block?tenantId=${data.tenantId}`, { method: 'POST', data });
}

import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface SupplierSiteManagerParams {
  keyword?: string;
  companyId: number;
  supplierIdList?: number[];
  pageNo?: number;
  pageSize?: number;
}

interface CategoryList {
  color: string;
  companyId: number;
  id: number;
  level: number;
  levelId: number;
  name: string;
  parentId: number;
  supplierLevel: {
    companyId: number;
    id: number;
    isBidding: number;
    isDefault: false;
    isDoorShow: number;
    isSign: number;
    name: string;
    type: number;
  };
  textColor: string;
  type: number;
}

export interface SupplierSiteManagerType {
  avatar: string;
  categoryList: CategoryList[];
  companyId: number;
  contactList: [];
  createTime: number;
  customerCategory: number;
  customerCompanyId: number;
  customerName: string;
  customerNo: string;
  customerUserId: number;
  id: number;
  isShare: false;
  lastEventTime: number;
  locked: number;
  phone: string;
  tags: [];
  teamId: number;
  userName: string;
}

/**
 * 建站控件查询供应商
 */
function getSupplierSiteManager(
  data: SupplierSiteManagerParams
): PaginationResponse<SupplierSiteManagerType> {
  return request('/v1/supplier/siteManager', {
    method: 'POST',
    data,
  });
}

export default getSupplierSiteManager;

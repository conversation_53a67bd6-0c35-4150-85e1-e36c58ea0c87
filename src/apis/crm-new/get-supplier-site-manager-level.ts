import request from './http';
import { PaginationResponse } from '../utils/utils';

export interface SupplierSiteManagerLvParams {
  keyword?: string;
  level: number;
}

export interface CategoryList {
  id: number;
  companyId: number;
  parentId: number;
  type: number;
  name: string;
  childList: CategoryList[];
}

/**
 * 建站控件查询供应商
 */
function getSupplierSiteManagerByLv(
  params: SupplierSiteManagerLvParams
): PaginationResponse<CategoryList> {
  return request('/v1/supplier/category/siteManager/by/level', {
    params,
    method: 'GET',
  });
}

export default getSupplierSiteManagerByLv;

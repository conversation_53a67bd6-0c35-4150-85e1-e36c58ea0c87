{"public_save": "Save", "public_save_successMsg": "Save successful", "public_cancel": "Cancel", "public_confirm": "Confirm", "public_delete": "Delete", "public_deleteSuccessMsg": "Delete successful", "public_deleteFailMsg": "Delete failed", "public_tip": "Tip", "public_more": "More", "public_edit": "Edit", "public_details": "Details", "public_create": "Create", "public_view": "View", "public_close": "Close", "public_open": "Open", "public_add": "Add", "public_desc": "Description", "public_filter": "Filter", "public-next": "Next", "public_previous": "Previous", "public_reset": "Reset", "public_search": "Search", "public_update": "Update", "public_copy": "Copy", "public_copySuccessMsg": "Copy successful", "public_copyFailMsg": "Co<PERSON> failed", "public_operate": "Action", "public_moneySymbol": "¥", "build_upload_getOBSFail": "Huawei Cloud OBS token retrieval failed", "build_upload_getClientFail": "Huawei Cloud OBS client instance retrieval failed", "build_upload_deleteOrCancleFail": "File {{failMsg}} failed", "build_upload_cancle": "Upload canceled", "build_upload_forbid": "Upload of .htm/.html files is forbidden", "build_upload_fail": "File upload failed", "build_upload_isUpload": "File not uploaded or already uploaded", "build_upload_browserUpdate": "Browser does not support folder uploads. Please update to the latest Chrome browser and try again", "build_custom_selectTip": "Please select a space page", "build_custom_noName": "Untitled page", "build_notes_coverTip": "Please upload a cover image", "build_notes_titleTip": "Please enter a title", "build_notes_relatedGoods": "Associate products", "build_notes_contain": "Including", "build_notes_apps": "Apps", "build_tabs_expand": "Expand sidebar", "build_template_designer": "Designer:", "build_template_blocked": "Blocked", "build_template_published": "Published", "build_template_preview": "Preview", "build_template_use": "Use", "build_template_publishNotes": "Publish note", "build_enter_purchased": "Purchased", "build_enter_create": "Created by me", "build_enter_loading": "Loading...", "build_enter_loaded": "End of the list", "build_enter_useTemplate": "Template used successfully", "build_enter_publishSuccess": "Published successfully", "build_header_publishTip": "Publish this page to the web, everyone can access your website via the link.", "build_header_publishing": "Publishing", "build_header_publishToWeb": "Publish to the web", "build_header_onLine": "This page is already online", "build_header_canclePublish": "Unpublish", "build_header_viewWeb": "View website", "build_header_publish": "Publish", "build_header_operateSuccessMsg": "Action successful", "build_header_delPageTip": "Are you sure you want to delete this page? It cannot be recovered after deletion.", "build_header_setSuccess": "Settings successful", "build_header_pageBack": "Page background", "build_header_imgTip": "Supports images up to 10MB, selected images will automatically fit screen size.", "build_header_backColor": "Background color", "build_header_seoSet": "SEO settings", "build_header_setHome": "Set as homepage", "build_header_copyLink": "Copy link", "build_header_moreOperate": "More actions", "build_header_descTips": "Website description refers to the description shown below search results when entering keywords in Baidu. A good description helps users judge whether your page content meets their needs, typically under 200 characters.", "build_header_keywordTips": "Keywords are the primary entry point for search engines to associate a website with content. When searching for a term, the search engine shows the page. Setting keywords is the first step. Tip: Start with location + company name or location + business type, typically under 100 characters. Example: Guangzhou XX construction reinforcement company, XX cultural planning, etc.", "build_header_noSeo": "When page SEO information is not set, website SEO information will be used.", "build_header_pageSeoSet": "Page SEO settings", "build_header_devideKeywords": "Use commas ',' to separate multiple keywords.", "build_header_seoKeywords": "SEO keywords", "build_header_keywordsInputTip": "Please enter SEO keywords", "build_header_seoDesc": "SEO description", "build_header_seoInputTip": "Please enter SEO description", "build_sider_rename": "<PERSON><PERSON>", "build_sider_delPageTip": "Are you sure you want to delete this page? It cannot be recovered after deletion.", "build_sider_inputPageName": "Please enter the page name", "build_sider_createSuccessMsg": "Created successfully", "build_sider_home": "Home", "build_sider_createPage": "Create page", "build_sider_createSonPage": "Create subpage", "build_sider_publishTemplate": "Publish template", "build_sider_changeSuccessMsg": "Change successful", "build_sider_delCurrentPageTip": "Are you sure you want to delete the current page? All subpages under this page will also be deleted.", "build_sider_delPage": "Delete page", "build_sider_selectPage": "Please select a page", "build_sider_create": "Create", "build_sider_iconAndName": "Space icon and name", "build_sider_spaceNameInput": "Please enter the space name", "build_sider_desc": "Description", "build_sider_spaceIntro": "Please enter space description (optional)", "build_sider_createSpace": "Create space", "build_sider_spaceSet": "Space settings", "build_sider_deldetSpace": "Delete space", "build_sider_deleteTip": "Deleting this space will prevent further use, and all pages within the space will be deleted. Are you sure you want to delete this space?", "build_sider_putAway": "Collapse sidebar", "build_sider_set": "Settings", "build_sider_market": "Template plaza", "build_sider_companyTemp": "Enterprise templates", "build_sider_teamSpace": "Team space", "build_sider_noData": "No content available,", "build_sider_createNow": "Create space now", "build_sider_changeWord": "No related data, try another keyword～", "build_operate_category": "Category management", "build_operate_createCategory": "Create category", "build_operate_categoryName": "Category name", "build_operate_homeView": "Display on homepage", "build_operate_updateSuccess": "Update successful", "build_operate_createTempCate": "Create template category", "build_operate_createSuccess": "Created successfully", "build_operate_editTempCate": "Edit template category", "build_operate_cateNameInput": "Please enter the category name", "build_operate_cardTop": "Card header", "build_operate_tag": "Tags", "build_operate_img": "Image", "build_operate_imgTip": "Supports png, jpg, and other formats. Recommended aspect ratio 1", "build_operate_tagInput": "Please enter tag content", "build_operate_change": "Change", "build_operate_tailor": "Crop", "build_operate_addImg": "Add image", "build_operate_cateDesc": "Category description", "build_operate_cateDescInput": "Please enter category description", "build_operate_cardBack": "Card background", "build_operate_imgTip2": "Supports png, jpg, and other formats. Recommended size 154*216 PX", "build_operate_uploadTip": "Please upload card background", "build_operate_ifSure": "Are you sure", "build_operate_thisTemp": "This template", "build_operate_cancleShield": "Unblock", "build_operate_shield": "Block", "build_operate_cancleShieldSuccess": "Unblocked successfully", "build_operate_shieldSuccess": "Blocked successfully", "build_operate_tempName": "Template name", "build_operate_publisher": "Publisher", "build_operate_tempCate": "Template category", "build_operate_tempPrice": "Template price", "build_operate_appPrice": "Application price", "build_operate_totalPrice": "Total price", "build_operate_publishTime": "Release time", "build_operate_status": "Status", "build_operate_shielded": "Blocked", "build_operate_tempOperate": "Template operation", "build_operate_search": "Please search", "build_operate_topicName": "Topic name", "build_operate_topicType": "Topic type", "build_operate_user": "User created", "build_operate_official": "Official operation", "build_operate_notesNum": "Number of related notes", "build_operate_createTime": "Creation time", "build_operate_topicManage": "Topic management", "build_operate_newTopic": "Create topic", "build_operate_topicInputTip": "Please enter topic name", "build_operate_noPerm": "No permission", "build_operate_moPerm2": "No permission, please contact the administrator to enable", "build_operate_center": "Operation center", "build_setting_webSeo": "Website SEO", "build_setting_language": "Operation Settings", "build_setting_website_language": "Site Language", "build_setting_language_explain": "The product details, shopping cart, order details, and other pages on your website will display the language you have set", "build_setting_change_language_success": "Language switching successful", "build_setting_change_language_error": "Language switching failed", "build_setting_titleTip": "The browser title informs search engines of the main content of the page and is one of the primary references for search engines to assess page weight. Tip: Network name-location + service/product description, generally under 80 characters. Example: xx-Enterprise advertising planning, creating efficient marketing plans - xxx company, etc.", "build_setting_changeImg": "Change image", "build_setting_browerTitle": "Browser title", "build_setting_browerTitleInput": "Please enter browser title", "build_setting_imgTip": "Recommended size: 16*16px", "build_setting_browerIcon": "Browser icon", "build_setting_seoKeyword": "Website SEO keywords", "build_setting_seoDesc": "Website SEO description", "build_setting_attend": "Note: Submit the URL to Baidu after the website homepage is published", "build_setting_toBaidu": "Submit URL to Baidu", "build_setting_toSubmit": "Go to submit", "build_setting_search": "Index inquiry", "build_space_baseInfo": "Basic information", "build_market_createTip": "Please create a space first", "build_market_welcome": "Welcome to start the echOS digital business model", "build_tempSet_leaveTip": "Are you sure you want to leave the current page?", "build_tempSet_versionTip": "Please select the corresponding version of the application", "build_tempSet_versionTip2": "Please remove search criteria and then select the corresponding version of the application", "build_tempSet_agreementTip": "Please read and agree to the ISV Application Software License and Service Agreement", "build_tempSet_noteTip": "Do you want to publish a note for this template? It will only be promoted in the template plaza after publishing.", "build_tempSet_goNow": "Go now", "build_tempSet_publishTempSet": "Template publishing settings", "build_tempSet_change": "Replace", "build_tempSet_baseInfo": "Basic information", "build_tempSet_inputTip": "Please enter the template name", "build_tempSet_home": "Template homepage", "build_tempSet_homeSelect": "Please select a template homepage", "build_tempSet_selectTempCate": "Please select a template category", "build_tempSet_app": "Template application", "build_tempSet_messageTip": "(The version of the application products needed for the information blocks in the template)", "build_tempSet_searchAppName": "Search application name", "build_tempSet_appName": "Application name", "build_tempSet_selectVersion": "Select version", "build_tempSet_selectTip": "Please select", "build_tempSet_noApp": "Application not called", "build_tempSet_usePrice": "Application price", "build_tempSet_priceInputTip": "Please enter the price", "build_tempSet_agree": "I have read and agree to", "build_tempSet_agreement": "ISV Application Software License and Service Agreement", "build_tempSet_complete": "Complete", "build_tempSet_comfirmTip": "Are you sure you want to cancel publishing the note? After cancellation, the template you published will not be displayed in [Template Market] and cannot be accessed by customers. If you wish to continue publishing the template, please go to [Enterprise Templates] - [Created by me] to republish.", "build_extend_title": "Title", "build_extend_tip": "Enter text or '/' to trigger commands", "build_extend_block": "Basic block", "build_extend_nav": "Navigation", "build_extend_goods": "Products/Package", "build_extend_container": "Container", "build_extend_func": "Feature", "build_extend_messageBlock": "Application information block", "build_extend_dataCenter": "Data Center", "build_extend_comminuties": "Spark Note", "build_extend_beePrice": "Bee Inquiry and Quotation", "build_extend_purchase": "Eagle Eye Procurement", "build_extend_supplier": "Baichuan Supplier", "build_extend_news": "News Dynamics", "build_onlineFile_addFail": "Floating navigation already exists on the page, addition failed", "build_onlineFile_noImg": "No image uploaded", "build_onlineFile_buildManage": "Website management", "build_onlineFile_webManage": "Website management", "build_onlineFile_webDecoration": "Website decoration", "build_utils_errorTimes": "Error count reached 10 times", "build_utils_nodeNoFound": "Node with block {{ blockId }} not found"}
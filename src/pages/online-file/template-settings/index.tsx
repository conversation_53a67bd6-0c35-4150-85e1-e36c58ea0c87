import { useEffect, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Col, Form, Input, Row, Select, Button, message, Checkbox, Spin } from '@echronos/antd';
import { CheckboxChangeEvent } from '@echronos/antd/lib/checkbox';
import { Price, Modal, ImageCropper, SimpleUpload, ImageCropperHandle } from '@echronos/echos-ui';
import { getToken } from '@echronos/core';
import Icon from '@echronos/echos-icon';
import getTemplateClassList, {
  TemplateClassListType,
} from '@/apis/site-manager/get-template-classify-list';
import createSubmitTemplate from '@/apis/site-manager/create-submit-template';
import getTemplateAppList, {
  GetTemplateAppResultType,
} from '@/apis/site-manager/get-template-app-list';
import getTemplateInfo, { TemplateInfoResultType } from '@/apis/site-manager/get-template-info';
import publishManageNotes from '@/apis/cms/publish-manage-notes';
import PublishNotesModal from '../container/publish-notes-modal';
import styles from './index.module.less';

function TemplateSetting() {
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const webSiteId = searchParams.get('webSiteId') || '';
  const tenantId = searchParams.get('tenantId') || '';
  const spaceId = searchParams.get('spaceId') || '';
  const blockId = searchParams.get('blockId') || '';

  const imageCropperRef = useRef<ImageCropperHandle>(null);

  const [coverImage, setCoverImage] = useState('');
  const [templateClassList, setTemplateClassList] = useState<TemplateClassListType[]>([]);
  const [indexPageList, setIndexPageList] = useState([]);
  const [appList, setAppList] = useState<GetTemplateAppResultType[]>([]);
  const [searchAppList, setSearchAppList] = useState<GetTemplateAppResultType[]>([]);
  const [appPrice, setAppPrice] = useState(0);
  const [templatePrice, setTemplatePrice] = useState(0);
  const [agreeWith, setAgreeWith] = useState(false);
  const [isShowPublish, setIsShowPublish] = useState(false);
  const [templateInfo, setTemplateInfo] = useState({} as TemplateInfoResultType);
  const [pageLoading, setPageLoading] = useState(false);

  // 裁剪图片
  const onCropper = () => {
    imageCropperRef?.current?.onVisible();
  };

  // 搜索应用
  const onSearch = (words: string) => {
    if (words) {
      setAppList(searchAppList.filter((item: any) => item.name.indexOf(words) > -1));
    } else {
      setAppList(searchAppList);
    }
  };

  // 是否同意协议
  const onAgreeChange = (e: CheckboxChangeEvent) => {
    setAgreeWith(e.target.checked);
  };

  // 取消
  const onCancel = () => {
    Modal.confirm({
      title: `${t('public_tip')}`,
      centered: true,
      content: <div>{t('build_tempSet_leaveTip')}</div>,
      onOk: () => {
        localStorage.removeItem('INDEX_PAGE_LIST');
        navigate(`/file/template-market?webSiteId=${webSiteId}&tenantId=${tenantId}`);
      },
    });
  };

  // 确定完成模板设置
  const onOk = () => {
    const isAllSelect = appList.every((item) => item.isSelect);
    const isAllSearchSelect = searchAppList.every((item) => item.isSelect);
    if (!coverImage) return message.warning(t('build_notes_coverTip'));
    if (!isAllSelect) return message.warning(t('build_tempSet_versionTip'));
    if (!isAllSearchSelect) return message.warning(t('build_tempSet_versionTip2'));
    if (!agreeWith) return message.warning(t('build_tempSet_agreementTip'));
    form.validateFields().then(() => {
      const formInfo = form.getFieldsValue();
      setPageLoading(true);
      // 提交发布模板设置
      createSubmitTemplate({
        ...formInfo,
        tenantId,
        blockIds: indexPageList.map((item: { blockId: string }) => item.blockId),
        appInfos: appList.map((item) => ({
          appId: item.id,
          productId: item.productId,
          productType: 1,
        })),
        coverImage,
      })
        .then((res: { id: string }) => {
          localStorage.removeItem('INDEX_PAGE_LIST');
          // 查询发布笔记模板信息
          getTemplateInfo({ id: res.id }).then((success) => {
            setTemplateInfo(success);
            Modal.confirm({
              title: (
                <div style={{ display: 'flex' }}>
                  <Icon
                    name="success_prompt_fill"
                    color="#05d380"
                    size={20}
                    style={{ marginRight: '6px' }}
                  />
                  {t('public_tip')}
                </div>
              ),
              centered: true,
              content: <div>{t('build_tempSet_noteTip')}</div>,
              okText: `${t('build_tempSet_goNow')}`,
              onCancel: () => {
                navigate(
                  `/file/enterprise-template?webSiteId=${webSiteId}&tenantId=${tenantId}&activeType=3`,
                  { replace: true }
                );
              },
              onOk: () => {
                setIsShowPublish(true);
              },
            });
          });
        })
        .finally(() => {
          setPageLoading(false);
        });
    });
  };

  useEffect(() => {
    (async () => {
      setPageLoading(true);
      form.resetFields();
      // 查询首页列表(去除空间选项)
      const indexlist = await JSON.parse(localStorage.getItem('INDEX_PAGE_LIST') || '[]')?.filter(
        (item: { isSpace: boolean }) => !item.isSpace
      );
      setIndexPageList(indexlist);
      getTemplateClassList().then((res) => {
        setTemplateClassList(res.list);
      });

      getTemplateAppList({
        blockIds: indexlist.map((every: { blockId: string }) => every.blockId),
      })
        .then((res) => {
          setAppList(res.list);
          setSearchAppList(res.list);
        })
        .finally(() => {
          setPageLoading(false);
        });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spaceId, blockId]);

  return (
    <Spin spinning={pageLoading}>
      <div className={styles.templateSettingPage}>
        <Form form={form}>
          {/* 模板设置表单模块 */}
          <div className={styles.pageTitle}>{t('build_tempSet_publishTempSet')}</div>
          <div className={styles.pageMain}>
            <div className={styles.flexHeader}>
              <div className={styles.leftHeader}>
                <SimpleUpload
                  accept="image/png,image/jpeg,image"
                  maxCount={1}
                  onChange={(e) => setCoverImage(e?.file?.url)}
                  // @ts-ignore todo
                  obsFolder={import.meta.env.BIZ_OBS_FOLDER}
                  obsToken={getToken()}
                >
                  {coverImage ? (
                    <div className={styles.firstImageBox}>
                      <img src={coverImage} alt="" />
                      <div
                        className={styles.closeBox}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          setCoverImage('');
                        }}
                      >
                        <Icon name="close_line" color="#fff" />
                      </div>
                      <div className={styles.flexBtnBox}>
                        <div
                          className={styles.leftBtn}
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            onCropper();
                          }}
                        >
                          {t('public_edit')}
                        </div>
                        <div className={styles.line} />
                        <div className={styles.rightBtn}>{t('build_tempSet_change')}</div>
                      </div>
                    </div>
                  ) : (
                    <div className={styles.addFirstImageBox}>
                      <div>
                        <Icon name="add_line" color="#000" size={16} />
                        <div>{t('build_operate_addImg')}</div>
                      </div>
                    </div>
                  )}
                </SimpleUpload>
              </div>
              <div className={styles.rightHeader}>
                <div className={styles.rightHeaderTitle}>{t('build_tempSet_baseInfo')}</div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name="name"
                      label={t('build_operate_tempName')}
                      rules={[{ required: true }]}
                    >
                      <Input placeholder={t('build_tempSet_inputTip')} maxLength={10} showCount />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="indexBlock"
                      label={t('build_tempSet_home')}
                      rules={[{ required: true }]}
                    >
                      <Select
                        placeholder={t('build_tempSet_homeSelect')}
                        fieldNames={{ label: 'name', value: 'blockId' }}
                        options={indexPageList}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item
                  name="tempCategories"
                  label={t('build_operate_tempCate')}
                  rules={[{ required: true }]}
                >
                  <Select
                    mode="multiple"
                    placeholder={t('build_tempSet_selectTempCate')}
                    options={templateClassList}
                    fieldNames={{ label: 'name', value: 'id' }}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 精选应用 */}
            <div className={styles.templateAppBox}>
              <div className={styles.flexBox}>
                <div className={styles.boxTitle}>
                  <div>{t('build_tempSet_app')}</div>
                  <span className={styles.note}>{t('build_tempSet_messageTip')}</span>
                </div>
                <div>
                  <Input
                    allowClear
                    bordered={false}
                    className={styles.searchInput}
                    placeholder={t('build_tempSet_searchAppName')}
                    prefix={<Icon name="search_line" size={16} />}
                    onChange={(e) => {
                      onSearch(e.target.value);
                    }}
                  />
                </div>
              </div>
              {appList?.length ? (
                <div>
                  <div className={styles.tableHead}>
                    <div className={styles.appName}>{t('build_tempSet_appName')}</div>
                    <div className={styles.appVersion} style={{ textAlign: 'center' }}>
                      {t('build_tempSet_selectVersion')}
                    </div>
                  </div>
                  <div className={styles.tableBody}>
                    {appList.map((item) => (
                      <div className={styles.tableTr} key={item.id}>
                        <div className={styles.appName}>{item.name}</div>
                        <div className={styles.appVersion}>
                          <Select
                            value={item.productId}
                            options={item.goodsList}
                            placeholder={t('build_tempSet_selectTip')}
                            style={{ width: '100%' }}
                            fieldNames={{ label: 'name', value: 'id' }}
                            onChange={(value, info: any) => {
                              const newItem = item;
                              newItem.productId = value;
                              newItem.price = info.marketPrice;
                              newItem.isSelect = true;
                              setAppList([...appList]);
                              // 将用于搜索的数据与原数据保持一致
                              searchAppList.forEach((every) => {
                                if (every.id === info.id) {
                                  const searchItem = every;
                                  searchItem.productId = value;
                                  searchItem.price = info.marketPrice;
                                  searchItem.isSelect = true;
                                }
                              });
                              setSearchAppList([...searchAppList]);
                              const isAllSelect = appList.every((each) => each.isSelect);
                              if (isAllSelect) {
                                const appAllPrice = appList?.reduce(
                                  (acc, cur) => acc + (cur?.price || 0),
                                  0
                                );
                                setAppPrice(appAllPrice);
                              }
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className={styles.nullBox}>{t('build_tempSet_noApp')}</div>
              )}
            </div>
            {/* 价格展示模块 */}
            <div className={styles.flexPriceBox}>
              <div className={styles.lineBox}>
                <div>{t('build_tempSet_usePrice')}</div>
                {appPrice === 0 || appPrice ? (
                  <Price value={appPrice} className={styles.trendsPrice} separate format />
                ) : null}
              </div>
              <div className={styles.priceSum}>{t('build_operate_totalPrice')}</div>
            </div>
            <div className={styles.flexPriceBox}>
              <Form.Item
                name="tempPrice"
                label={t('build_operate_tempPrice')}
                rules={[{ required: true }]}
              >
                <Input
                  placeholder={t('build_tempSet_priceInputTip')}
                  prefix={t('public_moneySymbol')}
                  onBlur={(e) => setTemplatePrice(Number(e.target.value))}
                />
              </Form.Item>
              <Price
                value={Number(templatePrice + appPrice)}
                className={styles.totalPrice}
                separate
                format
              />
            </div>
          </div>

          <div className={styles.agreeWithBox}>
            <Checkbox checked={agreeWith} onChange={onAgreeChange} />
            <div className={styles.agreeWithText}>{t('build_tempSet_agree')}</div>
            <div
              className={styles.agreement}
              onClick={() =>
                window.open(
                  'https://epoimages.obs.cn-south-1.myhuaweicloud.com/agreement/%E5%8D%8E%E5%8D%8E%E7%94%9F%E6%84%8F%E5%9C%88%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E8%AE%A2%E8%B4%AD%E5%8D%8F%E8%AE%AE.html'
                )
              }
            >
              {t('build_tempSet_agreement')}
            </div>
          </div>

          <div className={styles.pageFooter}>
            <Button htmlType="button" onClick={onCancel}>
              {t('public_cancel')}
            </Button>
            <div className={styles.finishBtn} onClick={onOk}>
              {t('build_tempSet_complete')}
            </div>
          </div>
        </Form>

        {/* 截图 */}
        <ImageCropper
          onSuccess={(value) => {
            setCoverImage(value.url);
          }}
          imgUrl={coverImage}
          ref={imageCropperRef}
          aspectType={0}
          // @ts-ignore todo
          obsFolder={import.meta.env.BIZ_OBS_FOLDER}
          obsToken={getToken()} // 项目中使用需从本地获取token传入
        />

        {isShowPublish && (
          <PublishNotesModal
            isVisible={isShowPublish}
            data={templateInfo}
            onConfirm={(val) => {
              publishManageNotes({ ...val }).then(() => {
                message.success(t('build_enter_publishSuccess'));
                navigate(
                  `/file/enterprise-template?webSiteId=${webSiteId}&tenantId=${tenantId}&activeType=3`,
                  { replace: true }
                );
              });
            }}
            onCancel={() => {
              Modal.confirm({
                title: `${t('public_tip')}`,
                centered: true,
                content: `${t('build_tempSet_comfirmTip')}`,
                onOk: () => {
                  setIsShowPublish(false);
                  navigate(
                    `/file/enterprise-template?webSiteId=${webSiteId}&tenantId=${tenantId}&activeType=3`,
                    { replace: true }
                  );
                },
              });
            }}
          />
        )}
      </div>
    </Spin>
  );
}

export default TemplateSetting;

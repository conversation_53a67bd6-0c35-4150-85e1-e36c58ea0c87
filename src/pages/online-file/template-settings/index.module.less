.templateSettingPage {
  padding: 16px 0 16px 0;
}

.addFirstImageBox {
  width: 122px;
  height: 162px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 10px;
  border: 1px solid #dadbe0;
  background: #f5f6fa;
  cursor: pointer;
}

.firstImageBox {
  width: 120px;
  height: 162px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  border: 1px solid #dadbe0;
  cursor: pointer;
}

.closeBox {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  opacity: 0.3;
  border-radius: 50%;
  position: absolute;
  top: 5px;
  right: 5px;
}

.flexBtnBox {
  width: 120px;
  position: absolute;
  bottom: 0;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: #000;
  opacity: 0.3;
  color: #fff;

  .line {
    width: 2px;
    height: 15px;
    margin: 0 16px;
    background-color: #fff;
  }
}

.pageTitle {
  padding-left: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f3f3;
  font-size: 18px;
  font-weight: 500;
  color: #040919;
}

.pageMain {
  padding: 20px 16px 0 16px;
}

.flexHeader {
  display: flex;
  margin-bottom: 20px;
}

.leftHeader {
  margin-right: 30px;
}

.rightHeader {
  flex: 1;
  padding-top: 14px;

  .rightHeaderTitle {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: 500;
  }
}

.templateAppBox {
  padding: 16px 20px;
  margin-bottom: 20px;
  border-radius: 20px;
  background: #f5f6fa;
}

.flexBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.boxTitle {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.note {
  font-size: 14px;
  color: #888b98;
  margin-left: 8px;
}

.searchInput {
  border-radius: 18px;
  background-color: #fff;

  &:hover {
    background-color: #fff;
  }
}

.tableHead {
  width: 100%;
  height: 38px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  border-radius: 10px;
  background-color: #fff;
  padding: 0 28px;
}

.tableTr {
  height: 72px;
  display: flex;
  align-items: center;
  padding: 0 28px;
}

.appName {
  width: 50%;
}

.appVersion {
  width: 50%;
}

.nullBox {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888b98;
}

.flexPriceBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lineBox {
  display: flex;
  align-items: center;
  padding-left: 10px;
  margin-bottom: 16px;
}

.trendsPrice {
  margin-left: 12px;
  color: #040919;
}

.totalPrice {
  font-size: 21px;
  font-weight: 500;
  color: #ea1c26;
}

.pageFooter {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreeWithBox {
  display: flex;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 10px;
}

.agreeWithText {
  margin-left: 5px;
}

.agreement {
  text-align: left;
  color: #441eff;
  cursor: pointer;
}

.finishBtn {
  margin-left: 10px;
  width: 88px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  color: #fff;
  background: linear-gradient(113deg, #441eff 0%, #823eff 100%);
  cursor: pointer;
}

import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import classNames from 'classnames';
import { Button, Switch, message } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { HolderOutlined } from '@ant-design/icons';
import getClassifyManagerList, {
  GetClassifyManagerListType,
} from '@/apis/site-manager/get-classify-manager-list';
import createTemplateClass from '@/apis/site-manager/create-template-classify';
import updateTemplateClassifysIsshow from '@/apis/site-manager/update-template-classify-isshow';
import updateTemplateClassifySort from '@/apis/site-manager/update-template-classify-sort';
import updateTemplateClassify from '@/apis/site-manager/update-template-classify';
import CreateEditClassModal from '../container/create-edit-class-modal';
import styles from './index.module.less';

interface ClassListDataType {
  customClassList: GetClassifyManagerListType[];
  otherClassList: GetClassifyManagerListType[];
}

function ClassMange() {
  const { t } = useTranslation();
  const navigator = useNavigate();

  const [isCreateShow, setIsCreateShow] = useState(false);
  const [isEditShow, setIsEditShow] = useState(false);
  const [classListData, setClassListData] = useState({} as ClassListDataType);
  const [classId, setClassId] = useState<string>('');

  // 获取分类管理列表
  const queryClassifyList = () => {
    getClassifyManagerList().then((res) => {
      setClassListData({
        customClassList: res.list.filter((item) => item.type === 2),
        otherClassList: [
          ...res.list.filter((item) => item.type !== 2 && item.isShow),
          ...res.list.filter((item) => item.type !== 2 && !item.isShow),
        ],
      });
    });
  };

  // 拖拽排序
  const onDragEnd = (result: DropResult) => {
    // 将元素放在无效位置，直接reture
    if (!result.destination) {
      return;
    }
    // 拖动元素不改变位置，同样也直接return
    const { source, destination } = result;
    if (source.index === destination.index) {
      return;
    }

    const newClassList = Array.from(classListData.otherClassList);
    const [movedItem] = newClassList.splice(result.source.index, 1);
    newClassList.splice(result.destination.index, 0, movedItem);
    setClassListData({
      customClassList: classListData.customClassList,
      otherClassList: newClassList,
    });
    updateTemplateClassifySort({
      ids: newClassList.map((item) => item.id),
    }).then(() => {
      message.success('排序成功');
      queryClassifyList();
    });
  };

  useEffect(() => {
    queryClassifyList();
  }, []);

  return (
    <div className={styles.rightPage}>
      <div className={styles.flexTitleBox}>
        <div className={styles.pageTitle}>
          <Icon
            name="left_arrow_line"
            size={20}
            color="#999eb2"
            style={{ marginRight: '10px', cursor: 'pointer' }}
            onClick={() => navigator('/operation-center/template-operation')}
          />
          {t('build_operate_category')}
        </div>
        <Button type="primary" size="small" onClick={() => setIsCreateShow(true)}>
          {t('build_operate_createCategory')}
        </Button>
      </div>
      <div className={styles.tableBox}>
        <div className={styles.tableTitleBox}>
          <div className={styles.className}>{t('build_operate_categoryName')}</div>
          <div className={styles.describe}>{t('public_desc')}</div>
          <div className={styles.homeShow}>{t('build_operate_homeView')}</div>
          <div className={styles.operate}>{t('public_operate')}</div>
        </div>
        <div className={styles.tableBodyBox}>
          {classListData?.otherClassList?.length || classListData?.customClassList?.length ? (
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    <div className={styles.tableBody}>
                      {/* 自定义分类 */}
                      {classListData?.customClassList?.map((item) => (
                        <div className={styles.tableTr} key={item.id}>
                          <div className={styles.className}>{item.name}</div>
                          <div className={styles.describe}>{item.describe}</div>
                          <div className={styles.homeShow}>
                            <Switch checked={Boolean(item.isShow)} disabled={item.type === 2} />
                          </div>
                          <div
                            className={classNames(styles.operate, styles.pointer, styles.clickBtn)}
                            onClick={() => {
                              setClassId(item.id);
                              setIsEditShow(true);
                            }}
                          >
                            {t('public_edit')}
                          </div>
                        </div>
                      ))}
                      {/* 其他分类列表 */}
                      {classListData?.otherClassList?.map((item, index) => (
                        <Draggable key={item.id} draggableId={item.id} index={index}>
                          {(provided) => (
                            <div
                              className={styles.tableTr}
                              key={item.id}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div className={styles.className}>
                                <HolderOutlined
                                  style={{
                                    color: '#999eb2',
                                    marginRight: '8px',
                                    cursor: 'pointer',
                                  }}
                                />
                                {item.name}
                              </div>
                              <div className={styles.describe}>{item.describe}</div>
                              <div className={styles.homeShow}>
                                <Switch
                                  checked={Boolean(item.isShow)}
                                  onChange={(value) => {
                                    updateTemplateClassifysIsshow({
                                      id: item.id,
                                      isShow: Number(value),
                                    }).then(() => {
                                      message.success(t('build_operate_updateSuccess'));
                                      queryClassifyList();
                                    });
                                  }}
                                />
                              </div>
                              <div
                                className={classNames(
                                  styles.operate,
                                  styles.pointer,
                                  styles.clickBtn
                                )}
                                onClick={() => {
                                  setClassId(item.id);
                                  setIsEditShow(true);
                                }}
                              >
                                {t('public_edit')}
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          ) : null}
        </div>
      </div>
      {isCreateShow && (
        <CreateEditClassModal
          title={t('build_operate_createTempCate')}
          isVisible={isCreateShow}
          onCloseModal={setIsCreateShow}
          onConfirm={(valInfo) => {
            createTemplateClass({
              ...valInfo,
            }).then(() => {
              message.success(t('build_operate_createSuccess'));
              setIsCreateShow(false);
              queryClassifyList();
            });
          }}
        />
      )}
      {isEditShow && (
        <CreateEditClassModal
          id={classId}
          title={t('build_operate_editTempCate')}
          isVisible={isEditShow}
          onCloseModal={setIsEditShow}
          onConfirm={(valInfo) => {
            updateTemplateClassify({
              id: classId,
              ...valInfo,
            }).then(() => {
              message.success(t('build_operate_updateSuccess'));
              setIsEditShow(false);
              queryClassifyList();
            });
          }}
        />
      )}
    </div>
  );
}

export default ClassMange;

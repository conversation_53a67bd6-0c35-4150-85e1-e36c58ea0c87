.rightPage {
  width: 100%;
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  overflow: hidden;
}

.flexTitleBox {
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pageTitle {
  font-size: 18px;
  font-weight: 500;
}

.tableBox {
  margin-top: 16px;

  .tableTitleBox {
    height: 38px;
    display: flex;
    align-items: center;
    border-radius: 10px;
    background: #f5f6fa;
  }

  .tableBodyBox {
    height: 100%;
  }

  .tableBody {
    height: 560px;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      width: 0;
    }
  }

  .tableTr {
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f4faff;
      transition: 0.3s;
    }
  }

  .className {
    width: 25%;
    padding-left: 20px;
    overflow: hidden;
  }

  .describe {
    width: 35%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .homeShow {
    width: 20%;
    text-align: center;
    overflow: hidden;
  }

  .operate {
    width: 20%;
    text-align: center;
    overflow: hidden;
  }
}

.clickBtn {
  color: #008cff;
}

.pointer {
  cursor: pointer;
}

import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { Button, Input, InputRef, message } from '@echronos/antd';
import { List, Modal } from '@echronos/echos-ui';
import { ColumnsType } from '@echronos/antd/es/table';
import Icon from '@echronos/echos-icon';
import getTemplatePageList, {
  GetTemplatePageResultType,
} from '@/apis/site-manager/get-template-page-list';
import updateTemplateShield from '@/apis/site-manager/update-template-shield';
import styles from './index.module.less';

function ManpowerMarket() {
  const { t } = useTranslation();
  const navigator = useNavigate();
  const listRef = useRef<any>(null);
  const wrapTable = useRef(null as unknown as HTMLDivElement);
  const searchRef = useRef(null as unknown as InputRef);
  const [isSearchShow, setIsSearchShow] = useState(false);
  const [tableBodyHeight, setTableBodyHeight] = useState(0); // 表格高度

  const params = useRef({
    pageSize: 10,
    searchKey: '',
  });

  // 搜索模板列表
  const onSearch = debounce((value: string) => {
    params.current.searchKey = value;
    listRef.current?.refresh();
  }, 300);

  // 操作是否屏蔽
  const onOperateShield = (value: GetTemplatePageResultType) => {
    Modal.confirm({
      title: `${t('public_tip')}`,
      content: (
        <div>
          {t('build_operate_ifSure')}
          {`${
            value.isShield ? `${t('build_operate_cancleShield')}` : `${t('build_operate_shield')}`
          }`}
          {t('build_operate_thisTemp')}
        </div>
      ),
      centered: true,
      onOk: () => {
        updateTemplateShield({ id: value.id }).then(() => {
          message.success(
            `${
              value.isShield
                ? `${t('build_operate_cancleShieldSuccess')}`
                : `${t('build_operate_shieldSuccess')}`
            }`
          );
          listRef.current?.refresh();
        });
      },
    });
  };

  const templateListColumns: ColumnsType<GetTemplatePageResultType> = [
    {
      align: 'center',
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (value) => <div>{value || '--'}</div>,
    },
    {
      align: 'center',
      title: '发布者',
      dataIndex: 'publisherName',
      key: 'publisherName',
      width: 100,
      render: (value) => (
        <div title={value} className={styles.publisherName}>
          {value || '--'}
        </div>
      ),
    },
    {
      align: 'center',
      title: '分类',
      dataIndex: 'tempClassVOS',
      key: 'tempClassVOS',
      width: 100,
      render: (value, record) => (
        <div>{record.tempClassVOS?.map((item) => item.name).join('、')}</div>
      ),
    },
    {
      align: 'center',
      title: '智能体数量',
      dataIndex: 'tempPrice',
      key: 'tempPrice',
      width: 100,
      render: (value) => <div>{Number(value).toFixed(2) || '--'}</div>,
    },
    {
      align: 'center',
      title: '价格',
      dataIndex: 'appPrice',
      key: 'appPrice',
      width: 100,
      render: (value) => <div>{Number(value).toFixed(2) || '--'}</div>,
    },
    {
      align: 'center',
      title: '上架时间',
      dataIndex: 'releaseTime',
      key: 'releaseTime',
      width: 100,
      render: (value) => (
        <div className={styles.releaseTime}>
          {dayjs(value).format('YYYY-MM-DD HH:mm:ss') || '--'}
        </div>
      ),
    },
    {
      align: 'center',
      title: `${t('build_operate_status')}`,
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (value, record) => (
        <span className={classNames(styles.statusBox, record.isShield ? styles.grey : styles.cyan)}>
          {record.isShield ? `${t('build_operate_shielded')}` : `${t('build_template_published')}`}
        </span>
      ),
    },
    {
      align: 'center',
      title: `${t('public_operate')}`,
      dataIndex: 'action',
      key: 'action',
      width: 80,
      render: (value, record) => (
        <div
          className={styles.btn}
          onClick={(e) => {
            e.stopPropagation();
            onOperateShield(record);
          }}
        >
          {record.isShield ? `${t('build_operate_cancleShield')}` : `${t('build_operate_shield')}`}
        </div>
      ),
    },
  ];

  // 计算table高度
  const resize = debounce(() => {
    if (wrapTable.current) {
      setTableBodyHeight(wrapTable.current.offsetHeight - 100);
    }
  }, 100);

  useEffect(() => {
    resize();
    window.addEventListener('resize', resize, false);
    return () => {
      window.removeEventListener('resize', resize, false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.rightPage}>
      <div className={styles.flexTitleBox}>
        <div className={styles.pageTitle}>硅基人力市场</div>
        <div className={styles.flexBox}>
          <div className={styles.searchBox}>
            {isSearchShow ? (
              <Input
                ref={searchRef}
                allowClear
                className={styles.searchInput}
                placeholder={t('build_operate_search')}
                prefix={<Icon name="search_line" size={16} />}
                onChange={(e) => {
                  onSearch(e.target.value);
                }}
                onBlur={(e) => {
                  if (!e.target.value) {
                    setIsSearchShow(false);
                  }
                }}
              />
            ) : (
              <div
                className={styles.iconBox}
                onClick={() => {
                  setIsSearchShow(true);
                  setTimeout(() => {
                    searchRef.current?.focus();
                  }, 0);
                }}
              >
                <Icon name="search_line" size={20} />
              </div>
            )}
          </div>
          <Button
            type="primary"
            size="small"
            onClick={() => navigator('/operation-center/agent-class-mange')}
          >
            新建分类
          </Button>
        </div>
      </div>
      <div ref={wrapTable} style={{ height: '100%' }}>
        <List
          ref={listRef}
          request={getTemplatePageList}
          columns={templateListColumns}
          params={params.current}
          scroll={{ y: `${tableBodyHeight - 50}px` }}
          onRow={() => {
            return {
              onClick: () => {
                // @ts-ignore todo
                // const link = `${import.meta.env.BIZ_FRONT_SITE}/render?rootBlockId=${
                //   record.indexId
                // }&tenantId=${record.tenantId}`;
                // openURL(link);
              },
            };
          }}
        />
      </div>
    </div>
  );
}

export default ManpowerMarket;

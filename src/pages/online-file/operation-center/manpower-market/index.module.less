.rightPage {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 18px;
  background-color: #fff;
}

.flexTitleBox {
  padding: 16px 16px 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pageTitle {
  font-size: 18px;
  font-weight: 500;
}

.flexBox {
  display: flex;
  align-items: center;
}

.searchBox {
  margin-right: 30px;
  cursor: pointer;
}

.searchInput {
  height: 30px;
  line-height: 30px;
  border-radius: 18px;
  padding: 0 12px !important;

  :global {
    .ant-input {
      line-height: 30px;
      font-size: 12px;
    }
  }
}

.publisherName {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.releaseTime {
  font-size: 12px;
  color: #888b98;
}

.statusBox {
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

.cyan {
  background-color: #e8ffea;
  color: #00b42a;
}

.grey {
  background-color: #f3f3f3;
  color: #040919;
}

.btn {
  color: #006eff;
  cursor: pointer;
}

.container {
  height: 100%;
  background-color: #fff;
  border-radius: 18px;
}

.flexTitleBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 0 16px;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.modalaWrap {
  :global {
    .ant-modal-content {
      border-radius: 18px;
    }
  }
}

.modalTitle {
  /* 中性色/040919 */
  color: #040919;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 10px;
}

.modalAddContent {
  border-radius: 12px;
  opacity: 1;
  background: #f5f6fa;
  padding-bottom: 8px;
}

.modalAddContentLength {
  color: #b1b3be;
  margin-right: 8px;
  text-align: right;
}

.modalBtns {
  display: flex;
  margin-top: 15px;
  justify-content: flex-end;
}

.modalBtns button {
  margin-left: 16px;
}

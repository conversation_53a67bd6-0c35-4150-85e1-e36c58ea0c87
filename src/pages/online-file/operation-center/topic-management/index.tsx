import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Modal, Input, message } from 'antd';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import { List } from '@echronos/echos-ui';
import { ColumnsType } from '@echronos/antd/es/table';
import saveTopic from '@/apis/cms/save-topic';
import getTopicList, { TopicItem } from '@/apis/cms/get-topic-list';
import styles from './index.module.less';

const { TextArea } = Input;

function TopicManagement() {
  const { t } = useTranslation();
  const listRef = useRef<any>(null);
  const wrapTable = useRef(null as unknown as HTMLDivElement);
  const params = useRef({
    keyword: '',
    tenantId: '', // 租户ID;
    pageNo: 1, // 页码;
    pageSize: 10, // 大小;
  });
  const [newTopicName, setNewTopicName] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false); // 新建弹窗

  const [loading, setLoading] = useState(false); // 加载状态
  const [tableBodyHeight, setTableBodyHeight] = useState(0); // 表格高度

  const addTopic = debounce(() => {
    setLoading(true);
    saveTopic({ name: newTopicName })
      .then(() => {
        message.success(t('build_operate_createSuccess'));
        setIsAddModalOpen(false);
        listRef.current?.refresh();
      })
      .finally(() => {
        setLoading(false);
      });
  }, 300);

  const columns: ColumnsType<TopicItem> = [
    {
      title: `${t('build_operate_topicName')}`,
      dataIndex: 'name',
      key: 'name',
      render: (text) => <div>{text}</div>,
    },
    {
      title: `${t('build_operate_topicType')}`,
      dataIndex: 'source',
      key: 'source',
      align: 'center',
      render: (text) => (
        <div>{text === 1 ? `${t('build_operate_user')}` : `${t('build_operate_official')}`}</div>
      ),
    },
    {
      title: `${t('build_operate_notesNum')}`,
      dataIndex: 'assNotesNum',
      key: 'assNotesNum',
      align: 'center',
      render: (text) => <div>{text}</div>,
    },
    {
      title: `${t('build_operate_createTime')}`,
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (text) => <div>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</div>,
    },
  ];

  // 计算table高度
  const resize = debounce(() => {
    if (wrapTable.current) {
      setTableBodyHeight(wrapTable.current.offsetHeight - 100);
    }
  }, 100);

  useEffect(() => {
    resize();
    window.addEventListener('resize', resize, false);
    return () => {
      window.removeEventListener('resize', resize, false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.flexTitleBox}>
        <div className={styles.title}>{t('build_operate_topicManage')}</div>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            setNewTopicName('');
            setIsAddModalOpen(true);
          }}
          className={styles.addBtn}
        >
          {t('build_operate_newTopic')}
        </Button>
      </div>
      <div ref={wrapTable} style={{ height: '100%' }}>
        <List
          ref={listRef}
          request={getTopicList}
          columns={columns}
          params={params.current}
          scroll={{ y: `${tableBodyHeight - 50}px` }}
        />
      </div>
      <Modal
        visible={isAddModalOpen}
        centered
        width={400}
        closable={false}
        footer={null}
        zIndex={999}
        wrapClassName={styles.modalaWrap}
        bodyStyle={{ borderRadius: '16px' }}
        onCancel={() => setIsAddModalOpen(false)}
      >
        <div className={styles.modalTitle}>{t('build_operate_newTopic')}</div>
        <div className={styles.modalAddContent}>
          <TextArea
            value={newTopicName}
            placeholder={t('build_operate_topicInputTip')}
            maxLength={30}
            bordered={false}
            onChange={(e) => setNewTopicName(e.target.value)}
            autoSize={{ minRows: 3, maxRows: 4 }}
          />
          <div className={styles.modalAddContentLength}>{newTopicName.length}/30</div>
        </div>
        <div className={styles.modalBtns}>
          <Button
            loading={loading}
            type="default"
            onClick={() => {
              setIsAddModalOpen(false);
            }}
          >
            {t('public_cancel')}
          </Button>
          <Button
            loading={loading}
            type="primary"
            onClick={() => {
              addTopic();
            }}
          >
            {t('public_confirm')}
          </Button>
        </div>
      </Modal>
    </div>
  );
}

export default TopicManagement;

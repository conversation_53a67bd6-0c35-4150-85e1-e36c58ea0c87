import { useEffect, useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Col, Row, message } from '@echronos/antd';
import { checkPermission } from '@echronos/core';
import Page from '@/components/page';
import styles from './index.module.less';

function OperationCenter() {
  const { t } = useTranslation();
  const menus = [
    {
      id: 1,
      title: `${t('build_operate_tempOperate')}`,
      to: '/operation-center/template-operation',
      code: 'BU_001_002',
    },
    {
      id: 2,
      title: `${t('build_operate_topicManage')}`,
      to: '/operation-center/topic-management',
      code: 'BU_001_001',
    },
    {
      id: 3,
      title: `人力市场分类管理`,
      to: '/operation-center/agent-class-mange',
      code: 'BU_001_003_001',
    },
  ];
  const navigator = useNavigate();
  const windowNavigate = (window.microApp as any)?.getData().navigate;

  const [menuId, setMenuId] = useState(1);

  const onLinkClick = (menu: any) => {
    if (checkPermission(menu.code)) {
      setMenuId(menu.id);
      navigator(menu.to);
    } else {
      message.warning(t('build_operate_noPerm'));
    }
  };

  useEffect(() => {
    if (checkPermission('BU_001_002')) {
      navigator('/operation-center/template-operation');
      setMenuId(1);
      return;
    }
    if (checkPermission('BU_001_001')) {
      navigator('/operation-center/topic-management');
      setMenuId(2);
      return;
    }
    if (checkPermission('BU_001_003_001')) {
      navigator('/operation-center/agent-class-mange');
      setMenuId(3);
      return;
    }
    windowNavigate('/');
    message.warning(t('build_operate_moPerm2'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Page
      title={[
        {
          title: `${t('build_operate_center')}`,
          to: '/build-web-site/operation-center/template-operation',
        },
      ]}
    >
      <Row className={styles.row}>
        <Col span={4} className={styles.menus}>
          {menus.map((menu) => (
            <div key={menu.id}>
              <div
                key={menu.id}
                className={menu?.id === menuId ? styles.activeMenu : styles.menu}
                onClick={() => {
                  onLinkClick(menu);
                }}
              >
                {menu.title}
              </div>
            </div>
          ))}
        </Col>
        <Col span={20} className={styles.content}>
          <Outlet />
        </Col>
      </Row>
    </Page>
  );
}

export default OperationCenter;

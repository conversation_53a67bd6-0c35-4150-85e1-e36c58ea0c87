import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, Spin } from '@echronos/antd';
import { Modal } from '@echronos/echos-ui';
// import Icon from '@echronos/echos-icon';
import { CreateTemplateClassParams } from '@/apis/site-manager/create-template-classify';
import { SolutionDataType } from '@/apis/ech-sbm';
import styles from './index.module.less';

interface CreateEditClassModalType {
  // eslint-disable-next-line react/require-default-props
  dataInfo?: SolutionDataType;
  title: string;
  isVisible: boolean;
  onCloseModal: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[value: CreateTemplateClassParams]>;
}

function CreateEditAgentClassModal({
  dataInfo,
  title,
  isVisible,
  onCloseModal,
  onConfirm,
}: CreateEditClassModalType) {
  window.console.log(dataInfo, 'dataInfo');
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const [loading, setLoading] = useState(true);

  const onCancel = () => {
    onCloseModal(false);
  };

  const onOk = () => {
    form.validateFields().then(() => {
      const formData = form.getFieldsValue();
      const formInfo = {
        ...formData,
      };
      onConfirm(formInfo);
    });
  };

  useEffect(() => {
    if (dataInfo?.id) {
      form.setFieldsValue({ name: dataInfo.name });
    }
    setLoading(false);
  }, [form, dataInfo]);

  return (
    <Modal
      title={title}
      width={600}
      visible={isVisible}
      onCancel={onCancel}
      onOk={onOk}
      className={styles.modal}
    >
      <Spin spinning={loading}>
        <Form form={form}>
          <Form.Item
            className={styles.formLabel}
            label={t('build_operate_categoryName')}
            name="name"
            rules={[
              { required: true, message: `${t('build_operate_cateNameInput')}` },
              {
                pattern: /\s*\S+/,
                message: '禁止输入空格',
              },
            ]}
            // extra={
            //   <div className={styles.correlationBox}>
            //     <div>已关联星火笔记话题</div>
            //     <div>话题名称: 财务法税</div>
            //     <div>ID: 12345</div>
            //     <div>
            //       <Icon name="success_prompt_fill" color="#008cff" /> 同步更新话题名称
            //     </div>
            //   </div>
            // }
          >
            <Input placeholder={t('build_operate_cateNameInput')} showCount maxLength={20} />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
}

export default CreateEditAgentClassModal;

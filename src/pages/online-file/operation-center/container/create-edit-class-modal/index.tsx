import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getToken } from '@echronos/core';
import { Form, Input, Radio, RadioChangeEvent, Spin } from '@echronos/antd';
import { Modal, ImageCropper, ImageCropperHandle, SimpleUpload } from '@echronos/echos-ui';
import Icon from '@echronos/echos-icon';
import getClassifyDetails from '@/apis/site-manager/get-classify-details';
import { CreateTemplateClassParams } from '@/apis/site-manager/create-template-classify';
import styles from './index.module.less';

const { TextArea } = Input;

interface CreateEditClassModalType {
  // eslint-disable-next-line react/require-default-props
  id?: string;
  title: string;
  isVisible: boolean;
  onCloseModal: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[value: CreateTemplateClassParams]>;
}

function CreateEditClassModal({
  id,
  title,
  isVisible,
  onCloseModal,
  onConfirm,
}: CreateEditClassModalType) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const imageCropperRef = useRef<ImageCropperHandle>(null);
  const backgroundCropperRef = useRef<ImageCropperHandle>(null);

  const [loading, setLoading] = useState(true);
  const [switchType, setSwitchType] = useState<number>(2);
  const [cardheadUrl, setCardheadUrl] = useState('');
  const [backgroundImage, setBackgroundImage] = useState('');
  const [ishowErr, setIshowErr] = useState(false);

  const onSwitch = (e: RadioChangeEvent) => {
    setSwitchType(e.target.value);
  };

  // 裁剪图片
  const onCropper = () => {
    imageCropperRef?.current?.onVisible();
  };

  const onBackgroundCropper = () => {
    backgroundCropperRef?.current?.onVisible();
  };

  const onCancel = () => {
    onCloseModal(false);
  };

  const onOk = () => {
    form.validateFields().then(() => {
      const formData = form.getFieldsValue();
      const formInfo = {
        ...formData,
        style: switchType,
        image: cardheadUrl,
        backgroundImage,
      };
      if (!backgroundImage) {
        setIshowErr(true);
        return;
      }
      onConfirm(formInfo);
    });
  };

  useEffect(() => {
    if (id) {
      getClassifyDetails({ id }).then((res) => {
        if (res.style === 1) {
          setCardheadUrl(res.image);
        }
        setBackgroundImage(res.backgroundImage);
        setSwitchType(res.style);
        form.setFieldsValue({ ...res });
        setLoading(false);
      });
    } else {
      setLoading(false);
    }
  }, [form, id]);

  return (
    <Modal
      title={title}
      width={600}
      visible={isVisible}
      onCancel={onCancel}
      onOk={onOk}
      className={styles.modal}
    >
      <Spin spinning={loading}>
        <Form form={form}>
          <Form.Item
            className={styles.formLabel}
            label={t('build_operate_categoryName')}
            name="name"
            rules={[{ required: true, message: `${t('build_operate_cateNameInput')}` }]}
          >
            <Input placeholder={t('build_operate_cateNameInput')} maxLength={8} showCount />
          </Form.Item>
          <Form.Item
            className={styles.formRadioLabel}
            label={t('build_operate_cardTop')}
            name="style"
          >
            <Radio.Group defaultValue={switchType} value={switchType} onChange={(e) => onSwitch(e)}>
              <Radio value={2}>{t('build_operate_tag')}</Radio>
              <Radio value={1}>
                <div className={styles.flexBox}>
                  <div>{t('build_operate_img')}</div>
                  <div className={styles.notice}>{t('build_operate_imgTip')}</div>
                </div>
              </Radio>
            </Radio.Group>
          </Form.Item>
          <div className={styles.secondaryBox}>
            {switchType === 2 ? (
              <Form.Item name="label">
                <Input placeholder={t('build_operate_tagInput')} maxLength={9} showCount />
              </Form.Item>
            ) : (
              <SimpleUpload
                accept="image/*"
                maxCount={1}
                onChange={(e) => {
                  setCardheadUrl(e?.file.url);
                }}
                // @ts-ignore todo
                obsFolder={import.meta.env.BIZ_OBS_FOLDER || 'test'}
                obsToken={getToken()}
              >
                {cardheadUrl ? (
                  <div className={styles.headimgBox}>
                    <img src={cardheadUrl} alt="" />
                    <div
                      className={styles.closeBox}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setCardheadUrl('');
                      }}
                    >
                      <Icon name="close_line" color="#fff" />
                    </div>
                    <div className={styles.flexBtnBox}>
                      <div className={styles.leftBtn}>{t('build_operate_change')}</div>
                      <div className={styles.line} />
                      <div
                        className={styles.rightBtn}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          onCropper();
                        }}
                      >
                        {t('build_operate_tailor')}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className={styles.addheadImgBox}>
                    <div>
                      <Icon name="add_line" color="#000" size={16} />
                      <div>{t('build_operate_addImg')}</div>
                    </div>
                  </div>
                )}
              </SimpleUpload>
            )}
          </div>
          <Form.Item
            className={styles.formLabel}
            label={t('build_operate_cateDesc')}
            name="describe"
            rules={[{ required: true, message: `${t('build_operate_cateDescInput')}` }]}
          >
            <TextArea
              className={styles.textAreaInput}
              placeholder={t('build_operate_cateDescInput')}
              maxLength={50}
              showCount
              rows={6}
            />
          </Form.Item>

          <div className={styles.flexLine}>
            <div className={styles.required}>*</div>
            <div className={styles.labelName}>{`${t('build_operate_cardBack')}(${
              backgroundImage ? 1 : 0
            }/1)`}</div>
            <div className={styles.notice}>{t('build_operate_imgTip2')}</div>
          </div>
          <div className={styles.secondaryBox}>
            <SimpleUpload
              accept="image/*"
              maxCount={1}
              onChange={(e) => {
                setBackgroundImage(e?.file?.url);
              }}
              // @ts-ignore todo
              obsFolder={import.meta.env.BIZ_OBS_FOLDER || 'test'}
              obsToken={getToken()}
            >
              {backgroundImage ? (
                <div className={styles.backgroundImgBox}>
                  <img src={backgroundImage} alt="" />
                  <div
                    className={styles.closeIconBox}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      setBackgroundImage('');
                    }}
                  >
                    <Icon name="close_line" color="#fff" />
                  </div>
                  <div className={styles.flexLineBox}>
                    <div>{t('build_operate_change')}</div>
                    <div className={styles.lineDrive} />
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        onBackgroundCropper();
                      }}
                    >
                      {t('build_operate_tailor')}
                    </div>
                  </div>
                </div>
              ) : (
                <div className={styles.addImgBox}>
                  <div>
                    <Icon name="add_line" color="#000" size={16} />
                    <div>{t('build_operate_addImg')}</div>
                  </div>
                </div>
              )}
            </SimpleUpload>
            {ishowErr && !backgroundImage ? (
              <div className={styles.redTip}>{t('build_operate_uploadTip')}</div>
            ) : null}
          </div>
        </Form>
        {/* 卡片头部裁剪 */}
        <ImageCropper
          onSuccess={(value) => {
            setCardheadUrl(value.url);
          }}
          imgUrl={cardheadUrl}
          ref={imageCropperRef}
          aspectType={0}
          // @ts-ignore todo
          obsFolder={import.meta.env.BIZ_OBS_FOLDER || 'test'}
          obsToken={getToken()} // 项目中使用需从本地获取token传入
        />
        {/* 背景裁剪 */}
        <ImageCropper
          onSuccess={(value) => {
            setBackgroundImage(value.url);
          }}
          imgUrl={backgroundImage}
          ref={backgroundCropperRef}
          aspectType={0}
          // @ts-ignore todo
          obsFolder={import.meta.env.BIZ_OBS_FOLDER || 'test'}
          obsToken={getToken()} // 项目中使用需从本地获取token传入
        />
      </Spin>
    </Modal>
  );
}

export default CreateEditClassModal;

.modal {
  :global {
    .ant-modal-content {
      .ant-modal-title {
        text-align: center;
      }

      .ant-modal-footer {
        text-align: center;

        .ant-btn {
          width: 160px;
        }
      }
    }
  }
}

.formLabel {
  :global {
    .ant-col {
      .ant-form-item-required {
        margin-right: 15px;
      }
    }
  }
}

.formRadioLabel {
  :global {
    .ant-form-item-label {
      margin-left: 15px;
      margin-right: 15px;
    }
  }
}

.flexBox {
  display: flex;
  align-items: center;
}

.secondaryBox {
  padding-left: 95px;
  margin-bottom: 20px;
}

.notice {
  margin-left: 8px;
  font-size: 12px;
  color: #888b98;
}

.headimgBox {
  width: 86px;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  background-color: #000;

  &:hover {
    .flexBtnBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;
      position: absolute;
      bottom: 2px;
    }

    .line {
      width: 1px;
      height: 12px;
      background: #fff;
      margin: 0 5px;
    }
  }

  &:hover {
    .closeBox {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #000;
      opacity: 0.3;
      border-radius: 50%;
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }
}

.addheadImgBox {
  width: 86px;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 10px;
  background: #f5f6fa;
  cursor: pointer;
}

.closeBox {
  display: none;
}

.flexBtnBox {
  display: none;
}

.textAreaInput {
  resize: none;

  :global {
    .ant-input {
      padding: 5.5px 12px;
    }
  }
}

.flexLine {
  line-height: 16px;
  display: flex;
  margin-bottom: 10px;
}

.required {
  color: #ea1c26;
  margin-right: 3px;
}

.backgroundImgBox {
  width: 100px;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  background-color: #000;

  &:hover {
    .flexLineBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;
      position: absolute;
      bottom: 5px;
    }

    .lineDrive {
      width: 1px;
      height: 12px;
      background: #fff;
      margin: 0 10px;
    }
  }

  &:hover {
    .closeIconBox {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #000;
      opacity: 0.3;
      border-radius: 50%;
      position: absolute;
      top: 10px;
      right: 5px;
    }
  }
}

.flexLineBox {
  display: none;
}

.closeIconBox {
  display: none;
}

.addImgBox {
  width: 100px;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 10px;
  background: #f5f6fa;
  cursor: pointer;
}

.redTip {
  margin-top: 2px;
  color: #ea1c26;
  font-size: 12px;
}

.row {
  height: 100%;
  margin-top: 5px;
  overflow: hidden;
}

.menus {
  height: 100%;
  padding-left: 16px;
}

.menus {
  height: 100%;
  overflow-y: auto;
}

.menu {
  color: #040919;
  font-size: 16px;
  display: block;
  height: 44px;
  line-height: 44px;
  padding-left: 13px;
  border-radius: 8px;
  font-weight: 600;
}

.activeMenu {
  .menu();

  background: rgba(255, 255, 255, 0.5);
  position: relative;

  &::before {
    content: '';
    width: 3px;
    height: 24px;
    position: absolute;
    top: 8px;
    left: 0;
    z-index: 1;
    background-color: #008cff;
    border-radius: 0 2px 2px 0;
  }
}

.content {
  height: 100%;
  padding: 0 16px 16px 16px;
}

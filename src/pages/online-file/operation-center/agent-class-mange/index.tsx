import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import classNames from 'classnames';
import { Button, Switch, message } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { HolderOutlined } from '@ant-design/icons';
import {
  getSolutionList,
  SolutionDataType,
  updateSolution,
  updateSolutionStatus,
  updateSolutionSort,
  createSolution,
} from '@/apis/ech-sbm';
import CreateEditAgentClassModal from '../container/create-edit-agent-class-modal';
import styles from './index.module.less';

function AgentClassMange() {
  const { t } = useTranslation();
  const navigator = useNavigate();

  const [isCreateShow, setIsCreateShow] = useState(false);
  const [isEditShow, setIsEditShow] = useState(false);
  const [solutionList, setSolutionList] = useState<SolutionDataType[]>([]);
  const [solutionInfo, setSolutionInfo] = useState({} as SolutionDataType);

  // 获取分类管理列表
  const querySolutionList = () => {
    getSolutionList().then((res) => {
      setSolutionList(res.list);
    });
  };

  // 拖拽排序
  const onDragEnd = (result: DropResult) => {
    // 将元素放在无效位置，直接reture
    if (!result.destination) {
      return;
    }
    // 拖动元素不改变位置，同样也直接return
    const { source, destination } = result;
    if (source.index === destination.index) {
      return;
    }

    const newClassList = Array.from(solutionList);
    const [movedItem] = newClassList.splice(result.source.index, 1);
    newClassList.splice(result.destination.index, 0, movedItem);
    setSolutionList(newClassList);
    updateSolutionSort({
      categorySorts: newClassList.map((item, index) => ({
        id: item.id,
        sort: index + 1,
      })),
    }).then(() => {
      message.success('排序成功');
      querySolutionList();
    });
  };

  useEffect(() => {
    querySolutionList();
  }, []);

  return (
    <div className={styles.rightPage}>
      <div className={styles.flexTitleBox}>
        <div className={styles.pageTitle}>
          <Icon
            name="left_arrow_line"
            size={20}
            color="#999eb2"
            style={{ marginRight: '10px', cursor: 'pointer' }}
            onClick={() => navigator('/operation-center/template-operation')}
          />
          人力市场分类管理
        </div>
        <Button type="primary" size="small" onClick={() => setIsCreateShow(true)}>
          {t('build_operate_createCategory')}
        </Button>
      </div>
      <div className={styles.tableBox}>
        <div className={styles.tableTitleBox}>
          <div className={styles.className}>{t('build_operate_categoryName')}</div>
          <div className={styles.count}>已上架方案数量</div>
          <div className={styles.status}>状态</div>
          <div className={styles.operate}>{t('public_operate')}</div>
        </div>
        <div className={styles.tableBodyBox}>
          {solutionList.length ? (
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    <div className={styles.tableBody}>
                      {/* 其他分类列表 */}
                      {solutionList?.map((item, index) => (
                        <Draggable key={item.id} draggableId={String(item.id)} index={index}>
                          {(provided) => (
                            <div
                              className={styles.tableTr}
                              key={item.id}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div className={styles.className} title={item.name}>
                                <HolderOutlined
                                  style={{
                                    color: '#999eb2',
                                    marginRight: '8px',
                                    cursor: 'pointer',
                                  }}
                                />
                                {item.name}
                              </div>
                              <div className={styles.count}>{item.count || 0}</div>
                              <div className={styles.status}>
                                <Switch
                                  checked={Boolean(item.status === 2)}
                                  onChange={(value) => {
                                    updateSolutionStatus({
                                      id: item.id,
                                      status: Number(value) === 1 ? 2 : 1,
                                    }).then(() => {
                                      message.success(t('build_operate_updateSuccess'));
                                      querySolutionList();
                                    });
                                  }}
                                />
                              </div>
                              <div
                                className={classNames(
                                  styles.operate,
                                  styles.pointer,
                                  styles.clickBtn
                                )}
                                onClick={() => {
                                  setSolutionInfo(item);
                                  setIsEditShow(true);
                                }}
                              >
                                {t('public_edit')}
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          ) : null}
        </div>
      </div>
      {isCreateShow && (
        <CreateEditAgentClassModal
          title="新建人力市场分类"
          isVisible={isCreateShow}
          onCloseModal={setIsCreateShow}
          onConfirm={(valInfo) => {
            createSolution({
              ...valInfo,
            }).then(() => {
              message.success(t('build_operate_createSuccess'));
              setIsCreateShow(false);
              querySolutionList();
            });
          }}
        />
      )}
      {isEditShow && (
        <CreateEditAgentClassModal
          dataInfo={solutionInfo}
          title="编辑人力市场分类"
          isVisible={isEditShow}
          onCloseModal={setIsEditShow}
          onConfirm={(valInfo) => {
            updateSolution({
              id: solutionInfo.id,
              ...valInfo,
            }).then(() => {
              message.success(t('build_operate_updateSuccess'));
              setIsEditShow(false);
              querySolutionList();
            });
          }}
        />
      )}
    </div>
  );
}

export default AgentClassMange;

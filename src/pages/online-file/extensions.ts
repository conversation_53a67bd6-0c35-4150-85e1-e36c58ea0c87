import i18n from 'i18next';
import { Color } from '@tiptap/extension-color';
import Focus from '@tiptap/extension-focus';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import FullColumnExtension from '@echronos/editor/dist/extension-full-column';
import FontSizeExtension from '@echronos/editor/dist/extension-font-size';
import FontFamilyExtension from '@echronos/editor/dist/extension-font-family';
import FontWeightExtension from '@echronos/editor/dist/extension-font-weight';
import SpacingExtension from '@echronos/editor/dist/extension-spacing';
import LineHeightExtension from '@echronos/editor/dist/extension-line-height';
import LetterSpacingExtension from '@echronos/editor/dist/extension-letter-spacing';
import { PluginKey } from '@tiptap/pm/state';
import {
  selectableNodeCls,
  getMenuBlocks,
  AppMenuItemType,
  BlockMenuItemType,
} from '@echronos/editor/dist/core';
import PasteExtension from '@echronos/editor/dist/extension-paste';
import InputExtension from '@echronos/editor/dist/extension-input';
import BehaviourModifyExtension from '@echronos/editor/dist/extension-behaviour-modify';
import ProcurementAnnouncementNode, {
  procurementNoticeNode,
  procurementResultsNode,
  qualificationReviewNode,
  procurementInquiryNode,
} from '@echronos/editor/dist/node-procurement';

import StatisticsNode from '@echronos/editor/dist/node-statistics';
import SuppliersNode from '@echronos/editor/dist/node-suppliers';
import SuppliersNavigationNode from '@echronos/editor/dist/node-suppliers-navigation';
import ArticleNode from '@echronos/editor/dist/node-article';
import CustomButtonNode from '@echronos/editor/dist/node-custom-button';
import SearchNode from '@echronos/editor/dist/node-search';
import SlashMenuExtension from '@echronos/editor/dist/extension-bubble-slash-menu';
import CodeBlockLowlight from '@echronos/editor/dist/extension-code-block-lowlight';
import ColumnBlockSchema from '@echronos/editor/dist/extension-column';
import PanelGroupNode from '@echronos/editor/dist/node-panel-group';
import PanelNode from '@echronos/editor/dist/node-panel';
import DivderNode from '@echronos/editor/dist/node-divider';
import ImageNode from '@echronos/editor/dist/node-image';
import SwiperNode from '@echronos/editor/dist/node-swiper';
import VideoNode from '@echronos/editor/dist/node-video';
import SwiperLoopNode from '@echronos/editor/dist/node-swiper-loop';
import ShopCardNode from '@echronos/editor/dist/node-shop-card';
import ShopPackageCardNode from '@echronos/editor/dist/node-shop-package-card';
import LogoNode from '@echronos/editor/dist/node-logo';
import FoldNavNode from '@echronos/editor/dist/node-fold-nav';
import FoldNavNodeAbout from '@echronos/editor/dist/node-fold-nav-v2';
import LinkBlockSchema from '@echronos/editor/dist/extension-link';
import SwiperCenteredNode from '@echronos/editor/dist/node-swiper-centered';
import SwiperMediumNode from '@echronos/editor/dist/node-swiper-medium';
import ThumbnailSwiperNode from '@echronos/editor/dist/node-thumbnail-swiper';
import ThumbnailSwiperVideoNode from '@echronos/editor/dist/node-thumbnail-swiper-video';
import CreateBlockIdExtension from '@echronos/editor/dist/extension-create-block-id';
import ParagraphNode from '@echronos/editor/dist/node-paragraph';
import AppendParagraphExtension from '@echronos/editor/dist/extension-append-paragraph';
import ContainerNode, {
  HeaderContainerNode,
  FooterContainerNode,
} from '@echronos/editor/dist/node-container';
import ContainerLayoutNode from '@echronos/editor/dist/node-container-layout';
import CategoryNode from '@echronos/editor/dist/node-category';
import PersonalCenterNode from '@echronos/editor/dist/node-personal-center';
import OnContentUpdateAllExtension from '@echronos/editor/dist/extension-on-content-update-all';
import HeadingNode from '@echronos/editor/dist/node-heading';
// import { NodeLinkNavLeft, NodeLinkNavTop } from '@echronos/editor/dist/node-link-nav';
import SparkNotesNode from '@echronos/editor/dist/node-spark-notes';
// import SwiperLoop from '@echronos/millet-ui/types/components/swiper-loop/swiper-loop';
// import NewsCard from '@echronos/editor/dist/node-news-card';
import NewsInfoCardNode from '@echronos/editor/dist/node-news-info-card';
import SelectNode from '@echronos/editor/dist/node-select';
import BannerImageNode from '@echronos/editor/dist/node-banner-image';

const extendCls = (extensionName: string, isDraggabe = true) =>
  `${isDraggabe ? selectableNodeCls : ''} node-${extensionName}`;

const commonClsConfig = () => ({
  HTMLAttributes: {
    class: selectableNodeCls,
  },
});

const extensionsConfig = [
  InputExtension,
  StarterKit.configure({
    // @ts-expect-error todo
    history: true,
    paragraph: {
      HTMLAttributes: {
        class: extendCls('paragraph'),
      },
    },
    heading: {
      levels: [1, 2, 3, 4, 5, 6],
      HTMLAttributes: {
        class: extendCls('heading'),
      },
    },
    horizontalRule: false,
    dropcursor: {
      color: '#4096ff',
      width: 2,
      class: 'block__dropcursor',
    },
    code: {
      HTMLAttributes: {
        class: 'block__inline-code',
      },
    },
    codeBlock: { ...commonClsConfig() },
    bulletList: {
      ...commonClsConfig(),
    },
  }),
  NewsInfoCardNode,
  ParagraphNode,
  HeadingNode,
  PanelGroupNode,
  PanelNode,
  AppendParagraphExtension,
  // EnterHandlerExtension,
  CreateBlockIdExtension,
  OnContentUpdateAllExtension,
  FontSizeExtension,
  FontFamilyExtension,
  FontWeightExtension,
  Color,
  Highlight.configure({ multicolor: true }),
  Underline,
  ContainerNode,
  ContainerLayoutNode,
  CategoryNode,
  CustomButtonNode,
  ArticleNode,
  SuppliersNode,
  SuppliersNavigationNode,
  StatisticsNode,
  ProcurementAnnouncementNode,
  procurementNoticeNode,
  procurementResultsNode,
  procurementInquiryNode,
  qualificationReviewNode,
  Placeholder.configure({
    placeholder({ node }) {
      if (node.type.name === 'heading') {
        return `${i18n.t('build_extend_title')}${node.attrs.level}`;
      }
      return `${i18n.t('build_extend_tip')}`;
    },
    emptyEditorClass: 'block__editor-empty',
    emptyNodeClass: 'block__node-empty',
    includeChildren: true,
  }),
  // 当前编辑聚集块选中高亮提示
  Focus.configure({
    className: 'block__focus',
    mode: 'deepest',
  }),
  LinkBlockSchema.configure({
    validate: (link) => /^https?:\/\//.test(link),
    HTMLAttributes: {
      class: 'block__link',
      'data-link': 'link',
    },
  }),
  CodeBlockLowlight,
  FullColumnExtension.configure({ types: ['paragraph'] }),
  ColumnBlockSchema,
  SlashMenuExtension.configure({
    suggestion: {
      char: '/',
      startOfLine: true,
      pluginKey: new PluginKey('slash'),
    },
    getSlashMenuList: async () => {
      try {
        const res = await getMenuBlocks({ abilityCode: 'cube_website_building', exportList: '' });
        const list = res.list;
        // 整合数据
        if (list.length) {
          // 基本块
          const baseMap: BlockMenuItemType = {
            id: 1,
            name: `${i18n.t('build_extend_block')}`,
            includesBlocks: [
              'paragraph',
              'heading1',
              'heading2',
              'heading3',
              'horizontalRule',
              'webLogo',
              'panel',
              'select',
            ],
            children: [],
          };

          // 图片
          const pictureMap: BlockMenuItemType = {
            id: 2,
            name: `${i18n.t('build_operate_img')}`,
            includesBlocks: [
              'image',
              'swiper',
              'hotZone',
              'video',
              'swiperLoop',
              'thumbnailSwiper',
              'thumbnailSwiperVideo',
              'swiperCentered',
              'swiperMedium',
              'bannerImage',
            ],
            children: [],
          };

          // 导航
          const navigatorMap: BlockMenuItemType = {
            id: 3,
            name: `${i18n.t('build_extend_nav')}`,
            includesBlocks: ['foldNav', 'floatNav', 'foldNavAbout'],
            children: [],
          };

          // 商品/套餐包
          const goodsPackageMap: BlockMenuItemType = {
            id: 4,
            name: `${i18n.t('build_extend_goods')}`,
            includesBlocks: [
              'selfGoodsDesignateProducts',
              'selfGoodsDesignateCategory',
              'selfGoodsSort',
              'selfGoodsPackDesignatePack',
              'selfGoodsPackDesignateCategory',
              'selfGoodsPackDesignateSort',
              'layGoodsDesignateProducts',
              'layGoodsDesignateCategory',
              'layGoodsDesignateSort',
              'layGoodsPackDesignatePack',
              'layGoodsPackDesignateCategory',
              'layGoodsPackDesignateSort',
              'category',
            ],
            children: [],
          };

          // 容器
          const containerMap: BlockMenuItemType = {
            id: 5,
            name: `${i18n.t('build_extend_container')}`,
            includesBlocks: ['container', 'headerContainer', 'footerContainer', 'containerLayout'],
            children: [],
          };

          // 功能
          const functionalityMap: BlockMenuItemType = {
            id: 6,
            name: `${i18n.t('build_extend_func')}`,
            includesBlocks: ['personalCenter', 'search', 'customButton'],
            children: [],
          };

          // 应用信息块
          const appMap: AppMenuItemType = {
            id: 7,
            name: `${i18n.t('build_extend_messageBlock')}`,
            includesApps: [
              `${i18n.t('build_extend_dataCenter')}`,
              `${i18n.t('build_extend_comminuties')}`,
              `${i18n.t('build_extend_beePrice')}`,
              `${i18n.t('build_extend_purchase')}`,
              `${i18n.t('build_extend_supplier')}`,
              `${i18n.t('build_extend_supplier')}`,
              `${i18n.t('build_extend_news')}`,
            ],
            children: [],
          };

          list.forEach((item) => {
            // 整合建站信息块
            item.blockList.forEach((iten) => {
              // eslint-disable-next-line no-param-reassign
              iten.classify = 'block';

              if (baseMap.includesBlocks.includes(iten.export)) {
                baseMap.children.push({
                  ...iten,
                  classify: iten.export === 'panel' ? 'renderMenu' : 'block',
                });
              }

              if (pictureMap.includesBlocks.includes(iten.export)) {
                pictureMap.children.push(iten);
              }

              if (navigatorMap.includesBlocks.includes(iten.export)) {
                navigatorMap.children.push(iten);
              }

              if (goodsPackageMap.includesBlocks.includes(iten.export)) {
                goodsPackageMap.children.push(iten);
              }

              if (containerMap.includesBlocks.includes(iten.export)) {
                // 排版布局容器不需要二级菜单
                containerMap.children.push({
                  ...iten,
                  classify: iten.export === 'containerLayout' ? 'block' : 'renderMenu',
                });
              }

              if (functionalityMap.includesBlocks.includes(iten.export)) {
                functionalityMap.children.push(iten);
              }
            });

            // 整合应用
            if (appMap.includesApps?.includes(item.appName)) {
              if (item.appName === `${i18n.t('build_extend_comminuties')}`) {
                appMap.children.push({ ...item, classify: 'renderMenu', type: 'sparkNotes' });
              }
              if (item.appName === `${i18n.t('build_extend_dataCenter')}`) {
                appMap.children.push({ ...item, classify: 'renderMenu', type: 'dataCenter' });
              }
              if (
                item.appName === `${i18n.t('build_extend_beePrice')}` ||
                item.appName === `${i18n.t('build_extend_purchase')}` ||
                item.appName === `${i18n.t('build_extend_supplier')}` ||
                item.appName === `${i18n.t('build_extend_news')}`
              ) {
                appMap.children.push({ ...item, classify: 'renderMenu', type: 'bidding' });
              }

              if (item.appName === '内容管理') {
                appMap.children.push({ ...item, classify: 'renderMenu', type: 'contentMgr' });
              }
            }
          });

          return {
            blockMenuList: [
              baseMap,
              pictureMap,
              navigatorMap,
              goodsPackageMap,
              containerMap,
              functionalityMap,
            ],
            appMenuList: [appMap],
          };
        }
        return { blockMenuList: [], appMenuList: [] };
      } catch (error) {
        console.warn('[getSlashMenuList]: ', error);
        return { blockMenuList: [], appMenuList: [] };
      }
    },
  }),
  DivderNode,
  ImageNode,
  SwiperNode,
  VideoNode,
  SwiperLoopNode,
  ShopCardNode,
  ShopPackageCardNode,
  FoldNavNode,
  FoldNavNodeAbout,
  LogoNode,
  HeaderContainerNode,
  FooterContainerNode,
  PersonalCenterNode,
  SwiperCenteredNode,
  SwiperMediumNode,
  ThumbnailSwiperNode,
  ThumbnailSwiperVideoNode,
  SearchNode,
  BehaviourModifyExtension,
  PasteExtension,
  TextAlign.configure({
    types: ['heading', 'paragraph'],
    alignments: ['left', 'center', 'right'],
  }),
  SpacingExtension.configure({
    types: ['heading', 'paragraph'],
  }),
  LineHeightExtension.configure({
    types: ['heading', 'paragraph'],
  }),
  LetterSpacingExtension.configure({
    types: ['heading', 'paragraph'],
  }),
  SparkNotesNode,
  SelectNode,
  BannerImageNode,
  // NodeLinkNavLeft,
  // NodeLinkNavTop,
  // ImageBlockSchema.configure({
  //   HTMLAttributes: {
  //     'data-type': 'image',
  //   },
  // }),

  // BubbleMenu.configure({
  //   pluginKey: 'bubbleMenuOne',
  //   shouldShow: ({ editor, view, state, oldState, from, to }) => {
  //     // only show the bubble menu for images and links
  //     // return editor.isActive('image') || editor.isActive('link');
  //     const { selection } = state;
  //     const { ranges } = selection;
  //     const from2 = Math.min(...ranges.map((range) => range.$from.pos));
  //     const to2 = Math.max(...ranges.map((range) => range.$to.pos));
  //     console.log('logo', from, to, from2, to2);
  //     // 检测节点是否是a标签
  //     // if (node.type.name === 'link') {
  //     //   return true;
  //     // }
  //     return true;
  //     // return editor.isActive('image') || editor.isActive('link');
  //   },
  // }),
];

export default extensionsConfig;

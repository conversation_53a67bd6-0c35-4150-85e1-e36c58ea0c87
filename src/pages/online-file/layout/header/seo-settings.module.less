.seo {
  :global .ant-input {
    font-size: 12px !important;
    padding: 5px 8px !important;
  }

  :global .ant-form-item {
    margin-bottom: 16px !important;
  }

  :global .ant-form-item-label {
    color: #040919;
    padding-bottom: 8px !important;

    label {
      line-height: 20px;
    }
  }

  :global .ant-form-item-control-input {
    min-height: 0;
  }

  :global .ant-form-item-extra {
    font-size: 12px;
    line-height: 20px;
    margin-top: 4px;
  }

  :global .ant-form-item:last-child {
    margin-bottom: 0 !important;
  }

  :global .ant-form-item:first-child {
    .ant-form-item-extra {
      margin-top: 0;
    }
  }

  :global .ant-input:hover {
    border-color: #823eff !important;
  }

  :global .ant-input:focus {
    border-color: #441eff !important;
  }

  .actions {
    display: flex;
    height: 36px;
    margin-top: 8px;
    margin-bottom: 20px;
    align-items: center;
    gap: 36px;
  }

  .link {
    color: #441eff;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.tooltip {
  display: flex;
  align-items: center;
  gap: 9px;
}

.icon {
  font-size: 20px;
}

.upload-smask {
  color: #fff;
  display: flex;
  justify-content: center;
  position: absolute;
  z-index: 9;
  transition: all 0.2s;
  inset: 0;
  opacity: 0;
  background: linear-gradient(0deg, rgb(0 0 0 / 50%), rgb(0 0 0 / 50%));
  align-items: center;
}

.upload {
  display: flex;
  width: 132px;
  height: 132px;
  overflow: hidden;
  justify-content: center;
  position: relative;
  transition: all 0.2s;
  border-radius: 10px;
  background: #f5f6fa;
  align-items: center;
  cursor: pointer;

  &:hover .upload-smask {
    opacity: 1;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
}

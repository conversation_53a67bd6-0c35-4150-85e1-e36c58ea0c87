/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable global-require */
import {
  PopoverS,
  BubbleMenuRow,
  getPageData,
  setPageData,
  useSiteInfo,
  useEditorState,
} from '@echronos/editor/dist/core';
import { useTranslation } from 'react-i18next';
import { Button, Col, Row, Typography } from 'antd';
import { useRequest } from 'ahooks';
import { useSearchParams } from 'react-router-dom';
import { publishPage, unPublishPage } from '@/apis';
import { useEffect, useState } from 'react';
import { CopyOutlined } from '@echronos/icons';
import Icon from '@/components/icon';
import styles from './deploy.module.less';

const { Paragraph } = Typography;

function HeaderDeployHandle() {
  const { t } = useTranslation();
  const { editor } = useEditorState();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId');
  const [open, setOpen] = useState(false);
  const page = getPageData();
  // 网站信息
  const domain = useSiteInfo((state) => state.domain);
  const [success, setSuccess] = useState(page.publishStatus === 1);
  const pageURL = `${window.location.protocol}//${domain}/render?rootBlockId=${page.pageId}&tenantId=${tenantId}`;
  const publish = useRequest(publishPage, { manual: true });
  const unpublish = useRequest(unPublishPage, { manual: true });

  const onPublish = async () => {
    await publish.runAsync({ rootBlockId: page.pageId, tenantId: tenantId || '' });
    setSuccess(true);
    setPageData({ ...page, publishStatus: 1 });
  };

  const onUnPublish = async () => {
    await unpublish.runAsync({ rootBlockId: page.pageId, tenantId: tenantId || '' });
    setSuccess(false);
    setPageData({ ...page, publishStatus: 0 });
  };

  const onCopy = () => {
    // message.success('复制成功');
  };

  const onToViewPage = () => {
    window.open(pageURL);
  };

  useEffect(() => {
    setSuccess(page.publishStatus === 1);
  }, [page]);

  return (
    <PopoverS
      destroyTooltipOnHide
      placement="bottomRight"
      trigger="click"
      visible={open}
      onVisibleChange={setOpen}
      content={() => (
        <BubbleMenuRow>
          {!success ? (
            <div className={styles['deploy-pre']}>
              <img
                className={styles['deploy-pre-icon']}
                src={require('./images/info.png')}
                alt="info"
              />
              <div className={styles['deploy-pre-info']}>
                <p>{t('build_header_publishTip')}</p>
                <div>
                  <Button
                    onClick={onPublish}
                    style={{ lineHeight: 1 }}
                    loading={publish.loading}
                    size="small"
                    className="gradual-primary"
                    type="primary"
                  >
                    {publish?.loading
                      ? `${t('build_header_publishing')}`
                      : `${t('build_header_publishToWeb')}`}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.deploy}>
              <div className={styles['deploy-inner']}>
                <img height={72} src={require('./images/deploy-success.png')} alt="success" />

                <p className={styles.title}>{t('build_enter_publishSuccess')}</p>

                <p className={styles.desc}>{t('build_header_onLine')}</p>

                <Paragraph
                  className={styles.url}
                  copyable={{
                    icon: <CopyOutlined style={{ color: '#999EB2' }} />,
                    onCopy,
                  }}
                >
                  {pageURL}
                </Paragraph>
              </div>
              <div className={styles.buttonBox}>
                <Button
                  loading={unpublish.loading}
                  onClick={onUnPublish}
                  size="large"
                  style={{ color: 'rgba(4, 9, 25, 0.6)' }}
                >
                  {t('build_header_canclePublish')}
                </Button>
                <Button
                  onClick={onToViewPage}
                  className="gradual-primary"
                  type="primary"
                  size="large"
                >
                  {t('build_header_viewWeb')}
                </Button>
              </div>
            </div>
          )}
        </BubbleMenuRow>
      )}
    >
      <Button
        className="gradual-primary deploy-btn"
        type="primary"
        onClick={() => {
          // eslint-disable-next-line no-console
          console.log('json', editor?.getJSON());
        }}
      >
        {t('build_header_publish')}
        {success && <Icon name="gouxuan" className={styles.publishStatus} />}
      </Button>
    </PopoverS>
  );
}

export default HeaderDeployHandle;

/* eslint-disable global-require */
import { useSiderStore } from '@/store/online-file/use-sider';
import { useTranslation } from 'react-i18next';
import { EyeOutlined, MenuOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { Breadcrumb, Col, Row, Space, Tooltip } from 'antd';
import { Editor as TiptapEditor } from '@tiptap/core';
import classNames from 'classnames';
import { openURL } from '@echronos/core';
import { useSearchParams } from 'react-router-dom';
import { getPageData } from '@echronos/editor/dist/core';
import HeaderDeployHandle from './deploy';
import HeaderMoreHandle from './handle';

import './header.less';

function WorkspaceHeaderWidget({
  slot,
  pageAttrs,
  editor,
  onPageAttrsChange,
}: {
  slot: string;
  editor: TiptapEditor;
  pageAttrs: Record<string, any>;
  onPageAttrsChange: (attrs: Record<string, any>) => void;
}) {
  const { t } = useTranslation();
  // 网站信息
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId');
  const { collapsed, levitate, setSiderToggle, setSiderLevitateShow, setSiderLevitateHide } =
    useSiderStore((state) => state);

  const preview = () => {
    const page = getPageData();
    // @ts-expect-error todo
    console.log('urlPath', import.meta.env.BIZ_FRONT_SITE);
    // @ts-expect-error todo
    const link = `${import.meta.env.BIZ_FRONT_SITE}/render?rootBlockId=${
      page.pageId
    }&tenantId=${tenantId}`;
    openURL(link);
  };

  return (
    <Row className="workspace-header" align="middle" slot={slot}>
      <Col>
        {collapsed && (
          <Tooltip title={t('build_tabs_expand')}>
            <div
              className={classNames('collapsed-trigger radius-icon', {
                collapsed: !!collapsed,
                active: !!levitate,
              })}
              onClick={() => setSiderToggle(!collapsed)}
              onMouseEnter={() => setSiderLevitateShow()}
              onMouseLeave={() => setSiderLevitateHide()}
            >
              {levitate ? <DoubleRightOutlined /> : <MenuOutlined />}
            </div>
          </Tooltip>
        )}
      </Col>
      <Col flex="1">
        <Breadcrumb>
          <Breadcrumb.Item className="breadcrumb">
            <img src={require('@/assets/file-icon/icon01.png')} alt="" />
            <span className="page-name">{pageAttrs.pageName}</span>
          </Breadcrumb.Item>
        </Breadcrumb>
      </Col>
      <Col>
        <Space size={16}>
          <Tooltip placement="top" title={t('build_template_preview')}>
            <EyeOutlined className="hd-anticon" onClick={preview} />
          </Tooltip>
          {/* <Tooltip placement="top" title="收藏">
            <StarOutlined className="hd-anticon" />
          </Tooltip> */}
          <HeaderDeployHandle />
          <HeaderMoreHandle
            editor={editor}
            pageAttrs={pageAttrs}
            onPageAttrsChange={onPageAttrsChange}
          />
        </Space>
      </Col>
    </Row>
  );
}

export default WorkspaceHeaderWidget;

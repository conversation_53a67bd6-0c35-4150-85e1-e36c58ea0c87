.deploy-pre {
  display: flex;
  padding: 16px 20px;
  box-sizing: border-box;
  width: 249px;
  height: 119px;
  &-icon {
    position: relative;
    top: 2px;
    margin-right: 8px;
    width: 16px;
    height: 16px;
  }
  &-info {
    align-items: flex-end;
    justify-content: space-between;
    display: flex;
    flex-direction: column;
  }
}

.deploy {
  box-sizing: border-box;
  width: 270px;
  padding: 14px 24px 32px;
  display: flex;
  align-items: center;
  flex-direction: column;
  line-height: 22px;
  justify-content: space-between;
}

.title {
  margin-top: 8px;
  color: var(--workspace-main-font-color);
}

.desc {
  color: var(--pm-text-gay-color);
  margin-top: 8px;
}

.url {
  word-break: break-all;
  margin-top: 8px;
  width: 222px;
  color: var(--workspace-main-font-color);
}

.deploy-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.publishStatus {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-left: 4px;
}

.buttonBox {
  margin-top: 15px;
  width: 100%;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 12px;
}

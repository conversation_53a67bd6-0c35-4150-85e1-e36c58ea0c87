.card {
  width: 220px;
  padding: 14px 16px;
  box-sizing: border-box;
}

.title {
  margin-bottom: 16px;
}

.label {
  color: var(--workspace-minor-font-color);
}

.bgset {
  display: flex;
  margin-top: 16px;
  margin-bottom: 19px;
  align-items: center;
}

.reset-btn {
  color: var(--workspace-primary-color);
  margin-right: 8px;
  margin-left: 22px;
  transition: opacity 0.1s;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.color-box {
  font-size: 12px;
  display: flex;
  padding: 4px 8px;
  transition: opacity 0.1s;
  background-color: var(--pm-text-bg-gay-color);
  border-radius: 8px;
  align-items: center;

  &:hover {
    opacity: 0.8;
  }

  &-color {
    border-radius: 4px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    border: 1px solid #000;
  }
}

.upload-btn {
  display: flex;
  width: 188px;
  height: 79px;
  justify-content: center;
  border-radius: 10px;
  align-items: center;
  background: #f5f6fa;
  cursor: pointer;
}

.color-select {
  width: 78px;
  position: absolute;
  cursor: pointer;
  opacity: 0;
}

.imgBox {
  max-height: 140px;
  overflow-y: auto;
  width: 100%;
}
.deleteIcon {
  font-size: 20px;
  line-height: 0;
}

.setIcon {
  color: #000;
  font-size: 20px;
  line-height: 0;
}

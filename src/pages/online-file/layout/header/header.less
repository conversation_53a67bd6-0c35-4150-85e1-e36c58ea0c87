@import '@/styles/variables';
@import '@/styles/mixins';

.workspace-header {
  height: 64px;
  padding: 16px;
  position: relative;

  .collapsed-trigger {
    cursor: pointer;
    line-height: 1;
    padding: 4px;
    transition: all 0.3s;

    &.active {
      background-color: var(--workspace-hover-color);
    }

    &.collapsed {
      position: static;
      transition: all 0.1s;
    }
  }

  .breadcrumb {
    padding-top: 0;
    padding-bottom: 0;

    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      vertical-align: -3px;
    }

    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: var(--workspace-main-font-color);
  }

  .deploy-btn {
    min-width: 76px;
  }

  .hd-anticon {
    padding: 6px;
    border-radius: 8px;
    font-size: 20px;
    cursor: pointer;

    &:hover {
      background-color: var(--workspace-hover-color);
    }
  }
}

.header-setting {
  width: 200px;
  padding: 0 8px;

  .title {
    color: var(--workspace-main-font-color);
    font-size: var(--default-font-size-px);
    margin-bottom: 16px;
  }

  .opt-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 0;
  }

  .rest-btn {
    color: var(--workspace-primary-color);
    padding: 0;
    min-height: auto;
    min-width: auto;
    margin-right: 8px;
  }

  .color-box {
    display: inline-block;
    vertical-align: middle;
    width: 78px;
    height: 28px;
    line-height: 28px;
    border-radius: 8px;
    text-align: center;
    background: #f5f6fa;
    cursor: pointer;
  }

  .color-grid {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background-color: aqua;
    border: 1px solid #000000;
    display: inline-block;
    vertical-align: text-bottom;
    margin-right: 4px;
  }

  .color-text {
    font-size: 12px;
    color: var(--workspace-main-font-color);
    vertical-align: top;
  }

  #select-color {
    width: 0;
    height: 0;
    padding: 0;
    background: none;
    opacity: 0;
    border: none;
  }

  .upload-btn {
    width: 178px;
    height: 79px;
    background: var(--pm-text-bg-gay-color);
    border-radius: 10px;
    text-align: center;
    line-height: 79px;
    cursor: pointer;
  }

  .img-box {
    width: 178px;
    height: 79px;
    border-radius: 10px;
    overflow: hidden;
    object-fit: contain;
    cursor: pointer;
    .img {
      width: 100%;
      height: 100%;
    }
  }
}

.header-deploy {
  border-radius: 18px;
  padding: 8px 12px;

  .deploy-warning {
    max-width: 210px;
    font-size: var(--default-font-size-px);

    .warning {
      color: #f9ae08;
    }

    .justify-end {
      padding-top: 19px;
      display: flex;
      justify-content: flex-end;

      .ant-btn {
        width: 100px;
      }
    }
  }

  .deploy-success {
    text-align: center;

    .title {
      color: var(--workspace-main-font-color);
    }

    .desc {
      padding: 8px 0;
      color: #888b98;
    }

    .page-url {
      color: #040919;
    }
  }
}

.deploy-popover {
  &.bg {
    .ant-popover {
      &-inner {
        &-content {
          background-image: url('./images/popup-bg.png');
          background-size: 100%;
        }
      }
    }
  }
}

@media screen and (max-width: 1400px) {
  .workspace-header {
    .breadcrumb {
      .ellipsis-multiple(240px ,1);
    }
  }
}

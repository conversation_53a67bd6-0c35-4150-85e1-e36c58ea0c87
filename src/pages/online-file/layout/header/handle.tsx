import { useTranslation } from 'react-i18next';
import { PopoverS, BubbleMenuRow, Cell, getPageData } from '@echronos/editor/dist/core';
import { DeleteOutlined, EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import Icon from '@/components/icon';
import { Editor as TiptapEditor } from '@tiptap/core';
import { useSearchParams } from 'react-router-dom';
import { message } from '@echronos/antd';
import confirm from '@/components/modal';
import { deleteSitePage, updateSitePage, updatePageBlock } from '@/apis';
import { Divider, Tooltip } from 'antd';
import { SimpleUpload } from '@/components/upload';
import { useState, useContext } from 'react';
import UploadFile from '@/components/upload/file/upload-file';
import { unsecuredCopyToClipboard } from '@/utils/tools';
import copy from 'copy-to-clipboard';
import { LinkOutlined } from '@echronos/icons';
import { useMount } from 'ahooks';
import { checkCharge, checkPermission } from '@echronos/core';
import PageContext from '../../context';
import styles from './handle.module.less';
import SeoSettings from './seo-settings';

// const pageColorTypes = [
//   {
//     label: '背景',
//     value: 'image',
//   },
//   {
//     label: '纯色',
//     value: 'color',
//   },
// ];

function HeaderMoreHandleContent({
  pageAttrs,
  onClose,
  editor,
  onOpenSeo,
  onPageAttrsChange,
}: {
  pageAttrs: Record<string, any>;
  editor: TiptapEditor;
  onClose: () => void;
  onPageAttrsChange: (attrs: Record<string, any>) => void;
  onOpenSeo: () => void;
}) {
  const { t } = useTranslation();
  const { updatePageList } = useContext(PageContext);
  // const [bgVal, setBgVal] = useState(pageAttrs.backgroundColor ? 'color' : 'image');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  const page = getPageData();
  const [bgVal] = useState('color');

  const { blockId, spaceId } = pageAttrs;
  const [color, setColor] = useState(pageAttrs.backgroundColor || '#fff');
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const [isShowSetting, setIsShowSetting] = useState(false);

  const updatePageAttrs = (attrs: Record<string, any>) => {
    const { blockId } = pageAttrs;
    const json = editor.getJSON();
    let content = [];
    if (json.content && json.content.length > 0) {
      content = json.content.map((it: any) => {
        // @ts-ignore
        return it.attrs.blockId;
      });
    }

    updatePageBlock({
      blockId, // 块ID
      spaceId, // 空间id
      rootBlockId: blockId, // 根页面块ID
      type: 'page',
      attrs,
      content,
      tenantId,
    }).then(() => {
      onPageAttrsChange(attrs);
    });
  };

  const onColorChange = (val: string) => {
    setColor(val);
    updatePageAttrs({
      ...pageAttrs,
      backgroundImage: '',
      backgroundColor: val,
    });
  };

  // const onBgChange = (val: string) => {
  //   setBgVal(val);
  //   if (val === 'color') {
  //     onColorChange(color);
  //   }
  // };

  const onCopyLink = () => {
    const url = window.location.href;
    try {
      if (window.isSecureContext && navigator.clipboard) {
        // navigator.clipboard.writeText(url);
        copy(url);
      } else {
        unsecuredCopyToClipboard(url);
      }
      message.success(t('public_copySuccessMsg'));
      onClose();
    } catch (error) {
      message.success(t('public_copyFailMsg'));
    }
  };

  const onDeletePage = () => {
    onClose();
    const onOk = async () => {
      await deleteSitePage({ blockId, spaceId, tenantId: tenantId || '' });
      updatePageList();
      message.success(t('build_header_operateSuccessMsg'));
    };
    const title = `${t('public_tip')}`;
    const content = `${t('build_header_delPageTip')}`;
    confirm({ title, content, onOk });
  };

  const setHome = () => {
    updateSitePage({
      spaceId,
      blockId,
      attrs: {
        ...pageAttrs,
        isHomePage: true,
      },
      tenantId: tenantId || '',
    }).then(() => {
      message.success(t('build_header_setSuccess'));
      updatePageList();
      onClose();
    });
  };

  const testPerm = async (code: string) => {
    if (!checkCharge(code)) {
      return false;
    }
    return checkPermission(code);
  };

  useMount(() => {
    testPerm('BC_001_003_008_001_001').then((res) => {
      setIsShowSetting(res);
    });
  });

  return (
    <BubbleMenuRow className={styles.card}>
      <div>
        <p className={styles.title}>{t('build_header_pageBack')}</p>
        {/* <Radio.Group
          onChange={(e) => {
            onBgChange(e.target.value);
          }}
          value={bgVal}
        >
          {pageColorTypes.map((k) => (
            <Radio key={`radio${k.value}`} value={k.value}>
              {k.label}
            </Radio>
          ))}
        </Radio.Group> */}
        {bgVal === 'image' && (
          <div className="opt-item" style={{ marginTop: 12 }}>
            <SimpleUpload
              className={styles.simpleUploadModal}
              onChange={(data: { file: UploadFile }) => {
                updatePageAttrs({
                  ...pageAttrs,
                  backgroundImage: data.file.url,
                  backgroundColor: '',
                });
              }}
            >
              {pageAttrs.backgroundImage ? (
                <div className={styles.imgBox}>
                  <img src={pageAttrs.backgroundImage} alt="" className="img" />
                </div>
              ) : (
                <div className={styles['upload-btn']}>
                  <PlusOutlined style={{ fontSize: 16, color: '#999EB2' }} />
                </div>
              )}
            </SimpleUpload>

            <p style={{ marginTop: 8, marginBottom: 16 }} className={styles.label}>
              {t('build_header_imgTip')}
            </p>
          </div>
        )}
        {bgVal === 'color' && (
          <div className={styles.bgset}>
            <div className={styles.label}>{t('build_header_backColor')}</div>
            <span
              className={styles['reset-btn']}
              onClick={() => {
                onColorChange('#fff');
              }}
            >
              {t('public_reset')}
            </span>
            <div className={styles['color-box']}>
              <input
                value={color}
                onChange={(e) => {
                  onColorChange(e.target.value);
                }}
                type="color"
                className={styles['color-select']}
              />
              <div style={{ background: color }} className={styles['color-box-color']} />
              <span>{color.substring(1).toUpperCase()}</span>
            </div>
          </div>
        )}
        <Divider />
      </div>
      {isShowSetting && (
        <Cell
          onClick={onOpenSeo}
          prefix={
            <img
              style={{ width: 20, height: 20 }}
              src="https://img.huahuabiz.com/user_files/2024527/1716774627934118.png"
              alt="src"
            />
          }
          title={t('build_header_seoSet')}
          hasArrow={false}
          hasHover={false}
        />
      )}

      {/* 已发布且非首页允许设置首页 */}
      {!pageAttrs.isHomePage && page.publishStatus === 1 && (
        <Cell
          prefix={<Icon className={styles.setIcon} name="shop" size={20} />}
          title={t('build_header_setHome')}
          onClick={setHome}
          hasArrow={false}
          hasHover={false}
        />
      )}
      <Cell
        onClick={onCopyLink}
        prefix={<LinkOutlined className={styles.setIcon} />}
        title={t('build_header_copyLink')}
        hasArrow={false}
        hasHover={false}
      />
      {/* 非首页允许删除 */}
      {!pageAttrs.isHomePage && (
        <>
          <Divider />
          <Cell
            onClick={onDeletePage}
            prefix={<DeleteOutlined className={styles.deleteIcon} />}
            className="page-delete"
            hoverBgColor="#fff"
            title={t('public_delete')}
            hasArrow={false}
            hasHover={false}
          />
        </>
      )}
    </BubbleMenuRow>
  );
}

function HeaderMoreHandle({
  pageAttrs,
  editor,
  onPageAttrsChange,
}: {
  pageAttrs: Record<string, any>;
  editor: TiptapEditor;
  onPageAttrsChange: (attrs: Record<string, any>) => void;
}) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [isOpenSeo, setIsOpenSeo] = useState(false); // 是否开启SEO设置弹窗

  return (
    <PopoverS
      placement="bottomRight"
      trigger="click"
      visible={open || isOpenSeo}
      onVisibleChange={(is: boolean) => {
        setOpen(is);
        if (!is) {
          setIsOpenSeo(is);
        }
      }}
      content={
        isOpenSeo ? (
          <SeoSettings blockId={pageAttrs.blockId} />
        ) : (
          <HeaderMoreHandleContent
            pageAttrs={pageAttrs}
            editor={editor}
            onOpenSeo={() => {
              setIsOpenSeo(true);
            }}
            onClose={() => {
              setOpen(false);
            }}
            onPageAttrsChange={onPageAttrsChange}
          />
        )
      }
    >
      <Tooltip placement="top" title={t('build_header_moreOperate')}>
        <EllipsisOutlined className="hd-anticon" />
      </Tooltip>
    </PopoverS>
  );
}

export default HeaderMoreHandle;

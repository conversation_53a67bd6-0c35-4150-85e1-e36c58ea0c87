import { BubbleMenuRow } from '@echronos/editor/dist/core';
import { useTranslation } from 'react-i18next';
import { Form, Input, Spin, Tooltip, Image } from 'antd';
import { InfoCircleOutlined, PlusOutlined } from '@echronos/icons';
import { useMemoizedFn, useRequest, useUnmount } from 'ahooks';
import getSeoByBlock from '@/apis/base/get-seo-by-block';
import { useEffect, useRef } from 'react';
import classNames from 'classnames';
import updateSeoByBlock from '@/apis/base/update-seo-by-block';
import { UpdateBySiteRequest } from '@/apis/base/update-seo-by-site';
import { SimpleUpload, SimpleUploadProps } from '@/components/upload';
import { useSearchParams } from 'react-router-dom';
import styles from './handle.module.less';
import settingsStyles from './seo-settings.module.less';

export interface SeoSettingsProps {
  blockId: string;
}

export default function SeoSettings(props: SeoSettingsProps) {
  const { t } = useTranslation();
  const TITLE_TIPS = `${t('build_setting_titleTip')}`;
  const DESCRIPTION_TIPS = `${t('build_header_descTips')}`;
  const KEYWORD_TIPS = `${t('build_header_keywordTips')}`;

  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const { blockId } = props;
  const info = useRequest(getSeoByBlock, { manual: true });
  const update = useRequest(updateSeoByBlock, { manual: true });
  const [form] = Form.useForm();

  const tempFormData = useRef<UpdateBySiteRequest>({});

  const TooltipRender = useMemoizedFn((p: { tips: string; label: string }) => (
    <div className={settingsStyles.tooltip}>
      <div>{p.label}</div>
      <Tooltip title={p.tips}>
        <InfoCircleOutlined />
      </Tooltip>
    </div>
  ));

  useEffect(() => {
    if (!blockId) return;
    info.runAsync(blockId, tenantId).then((res) => {
      form.setFieldsValue(res);
    });
    tempFormData.current = { blockId };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockId]);

  useUnmount(() => {
    const parmas = tempFormData.current;
    if (info.data?.id) {
      parmas.id = info.data?.id;
    }
    // // temp
    // parmas.browserTitle = ' ';
    update.run({ ...(parmas || {}), tenantId });
  });
  /** 上传图片 */
  const UploadRender = useMemoizedFn((p: { value?: string; onChange?: (v: string) => void }) => {
    const { value, onChange } = p;
    tempFormData.current.browserIcon = value;
    const handle: SimpleUploadProps['onChange'] = (v) => {
      onChange?.(v.file?.url || '');
    };

    return (
      <SimpleUpload
        className={classNames(settingsStyles.upload)}
        accept="image/*"
        maxCount={1}
        onChange={handle}
      >
        {value ? (
          <>
            <Image
              preview={false}
              width={120}
              height={120}
              className={settingsStyles['upload-img']}
              src={value}
              alt=""
            />
            <div className={settingsStyles['upload-smask']}>{t('build_setting_changeImg')}</div>
          </>
        ) : (
          <PlusOutlined className={settingsStyles.icon} />
        )}
      </SimpleUpload>
    );
  });

  return (
    <BubbleMenuRow className={styles.card}>
      <Spin spinning={info.loading}>
        <Form form={form} layout="vertical" className={settingsStyles.seo}>
          <Form.Item extra={t('build_header_noSeo')} label={t('build_header_pageSeoSet')} />
          <Form.Item
            name="browserTitle"
            label={<TooltipRender tips={TITLE_TIPS} label={t('build_setting_browerTitle')} />}
          >
            <Input
              autoComplete="off"
              placeholder={t('build_setting_browerTitleInput')}
              onChange={(e) => {
                tempFormData.current.browserTitle = e.target.value;
              }}
            />
          </Form.Item>
          <Form.Item
            name="browserIcon"
            extra={t('build_setting_imgTip')}
            label={t('build_setting_browerIcon')}
          >
            <UploadRender />
          </Form.Item>
          <Form.Item
            name="seoKeyword"
            extra={t('build_header_devideKeywords')}
            label={<TooltipRender tips={KEYWORD_TIPS} label={t('build_header_seoKeywords')} />}
          >
            <Input.TextArea
              onChange={(e) => {
                tempFormData.current.seoKeyword = e.target.value;
              }}
              placeholder={t('build_header_keywordsInputTip')}
              autoSize={{ minRows: 4, maxRows: 4 }}
            />
          </Form.Item>
          <Form.Item
            name="seoDesc"
            label={<TooltipRender tips={DESCRIPTION_TIPS} label={t('build_header_seoDesc')} />}
          >
            <Input.TextArea
              onChange={(e) => {
                tempFormData.current.seoDesc = e.target.value;
              }}
              placeholder={t('build_header_seoInputTip')}
              autoSize={{ minRows: 4, maxRows: 4 }}
            />
          </Form.Item>
        </Form>
      </Spin>
    </BubbleMenuRow>
  );
}

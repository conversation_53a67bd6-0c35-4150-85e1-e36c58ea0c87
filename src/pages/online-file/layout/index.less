.header {
  background: #fff;
  padding-left: 6px;
}

.content {
  margin: 10px;
  background-color: #fff;
  border-radius: 5px;
}

.page-container {
  background: none !important;
  border-radius: 0 !important;
  backdrop-filter: none !important;
}

.workspace {
  &-wrapper {
    height: 100%;
    overflow-x: hidden;
    background-color: rgb(255 255 255 / 60%);
    border-radius: 18px;
    border: 1px solid rgb(255 255 255 / 90%);
  }

  &-main {
    min-width: 375px;
    border-radius: 0 18px 18px 0;
    background: #fff;
    box-shadow: 0 4px 16px 0 rgb(4 2 33 / 10%);
    position: relative;

    &__body {
      overflow: hidden scroll;
      // position: relative;
    }
  }

  .ant-layout {
    height: 100%;
  }
}

import { Page } from '@echronos/react';
import { PropsWithChildren, ReactElement } from 'react';
import { Layout } from '@echronos/antd';
import './index.less';

const { Sider, Content } = Layout;

interface ILayoutMap {
  sider: ReactElement;
  header: ReactElement;
  main: ReactElement;
}

function WorkspacesLayout({ children, collapsed }: PropsWithChildren<{ collapsed: boolean }>) {
  const slotMap = {} as ILayoutMap;
  const mapSlot = () => {
    let child: ReactElement[] = [];
    if (typeof children === 'object' && !Array.isArray(children)) {
      child = [children as ReactElement];
    } else {
      child = children as ReactElement[];
    }
    if (child) {
      child.forEach((el) => {
        if (el) {
          const { slot } = el.props;
          slotMap[slot as unknown as keyof ILayoutMap] = el;
        }
      });
    }
  };
  mapSlot();

  /**
   * @param slotName 创建插槽
   * @returns
   */
  const createSlot = (slotName: keyof ILayoutMap) => {
    if (slotMap[slotName]) {
      return slotMap[slotName];
    }
    return null;
  };

  return (
    <Page pageKey="build-site" className="page-container">
      <Layout className="workspace-wrapper">
        <Sider
          trigger={null}
          collapsible
          theme="light"
          width="290px"
          collapsed={collapsed}
          breakpoint="lg"
          collapsedWidth="0"
          style={{ maxWidth: '300px' }}
          className="workspace-sider"
        >
          {createSlot('sider')}
        </Sider>
        <Layout className="workspace-main">
          {createSlot('header')}
          <Content className="workspace-main__body">{createSlot('main')}</Content>
        </Layout>
      </Layout>
    </Page>
  );
}

export default WorkspacesLayout;

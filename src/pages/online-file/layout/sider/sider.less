@import '@/styles/variables';
@import '@/styles/mixins/index.less';

.sider-widget {
  // min-height: 60vh;
  width: 290px;
  height: 100%;
  padding: 12px 12px 0;
  position: relative;
  background-image: url('./images/sider-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 0 0;
  border-radius: 18px 0 0 18px;

  &.preload {
    height: calc(100% - 64px);
    min-height: 60vh;
    position: absolute;
    top: 64px;
    left: -395px;
    z-index: 9999;
    transition: left 0.4s;
    box-shadow: 14px 20px 24px 0 rgb(2 9 58 / 16%);
    border-radius: 0 18px 18px 0;
    background-color: rgb(255 255 255 / 99%);

    &:before {
      content: '';
      display: block;
      width: 100%;
      height: 22px;
      position: absolute;
      top: -22px;
      left: 0;
    }
  }

  &.levitate {
    left: 0;
  }

  // background: #F5F6FA;
  .chunk-margin {
    margin-bottom: 10px;
  }

  &__site {
    padding: 4px 8px 0;
    .flex-layout();

    .ant-col {
      line-height: 0;

      &:nth-child(2n) {
        flex: 1;
      }

      &:last-child {
        padding: 0 !important;
      }
    }

    img {
      border-radius: 50%;
    }

    h3 {
      color: var(--workspace-main-font-color);
      font-size: var(--default-font-size-lg);
      font-weight: 600;
    }
  }

  .sider-search {
    line-height: 30px;
    margin-bottom: 4px;
    padding: 5px 8px;
    border: none;
    border-radius: 8px;
    background-color: transparent;

    ::placeholder {
      color: var(--workspace-base-font-color);
    }

    .anticon {
      font-size: 15px;
      margin-right: 9px;
    }

    .ant-input {
      background-color: transparent;
      font-size: var(--default-font-size-px);

      &::input-placeholder {
        color: rgba(4 9 25 / 80%);
        font-weight: 500;
      }
    }

    .ant-input-prefix {
      margin-right: 0 !important;
    }

    &.ant-input-affix-wrapper {
      &-focused {
        background-color: #fff;
      }
    }

    &:not(.ant-input-affix-wrapper-focused):hover {
      background-color: var(--workspace-hover-color);
    }
  }

  .sider-add-page {
    color: var(--workspace-main-font-color);
    display: block;
    line-height: 30px;
    padding: 5px 8px;
    border-radius: 8px;
    cursor: pointer;

    .anticon {
      font-size: 15px;
      margin-right: 9px;
    }

    &:hover {
      background-color: var(--workspace-hover-color);
      color: var(--workspace-primary-color);
    }
  }

  &__no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60%;

    &-text {
      font-size: 14px;
      font-weight: 400;
      color: #040919;
      // 箭头
      cursor: default;
      span {
        color: #008cff;

        cursor: pointer;
      }
    }
  }

  &__page {
    height: calc(100% - 220px);

    .category-title {
      display: flex;
      justify-content: space-between;
      font-weight: 600;
      line-height: 24px;
      padding: 9px 0 11px 8px;
      cursor: pointer;

      &:hover .spaceAdd {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .spaceAdd {
      display: none;
      width: 20px;
      height: 20px;
      &:hover {
        background-color: rgba(#b1b3be, 0.3);
        border-radius: 4px;
      }
      border-radius: 4px;
    }

    .spaceAddIcon {
      font-size: 16px;
      color: #000 !important;
    }

    .ant-tree.page-tree {
      background-color: transparent;
      height: calc(100% - 45px);
      overflow-y: auto;

      .anticon {
        vertical-align: middle;
      }

      .ant-tree {
        &-switcher {
          display: none;
        }

        &-node-content-wrapper {
          color: #999eb2 !important;
          width: 0;
          height: 40px;
          line-height: 1.5;
          line-height: 40px;
          padding: 0 6px;
        }

        &-switcher .anticon {
          font-size: 16px;
        }

        &-treenode {
          margin-bottom: 4px;
          padding: 6px 8px;
          align-items: center;
          border-radius: 8px;

          &:before {
            bottom: 0;
            border-radius: 8px;
          }

          &-selected {
            color: #000;

            &:before,
            &:hover:before {
              background: #fff;
            }
          }

          &:not(.ant-tree-treenode-selected):hover {
            &:before {
              background-color: var(--workspace-hover-color);
              border-radius: 8px;
            }
          }

          &.progress,
          &:hover {
            .tree-suffix {
              visibility: visible;
            }

            .tree-suffix .anticon,
            .ant-tree-switcher {
              color: #000;
            }
          }
        }

        &-node-selected {
          background-color: #fff;
        }
      }
    }
  }

  &__rename {
    display: flex;
    display: none;
    min-width: 350px;
    padding: 4px 8px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    flex-direction: column;
    background: #ecf1f4;
    box-shadow: -8px 8px 24px 0 rgb(2 9 58 / 16%);
    border-radius: 10px;

    &.show {
      display: block;
    }

    .rename-input {
      min-height: 32px !important;
      padding: 0 12px;
      line-height: 2;
      border-radius: 6px;
      border: 1px solid #b1b3be;
    }

    .page-icon {
      width: 32px;
      height: 32px;
      margin-right: 4px;
      padding: 6px;
      border-radius: 6px;
      background: #fff;
      border: 1px solid #b1b3be;

      img {
        width: 20px;
        height: 20px;
        vertical-align: top;
      }
    }
  }

  &__name {
    .ellipsis-multiple(100%, 1, -webkit-inline-box);
    font-family: '苹方-简';
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    color: #040919;
  }

  &__organization {
    font-family: '苹方-简';
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0em;
    color: rgba(4, 9, 25, 0.7);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 2px;
  }

  &__selected-item {
    background-color: #fff !important;
  }

  &__icon-wrap {
    display: none;

    .close-icon {
      color: #000;
      font-size: 14px;
      margin-top: 2px;
      padding: 4px;
      transition: background 0.1s;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background: var(--workspace-hover-color);
      }

      &:active {
        background: var(--workspace-hover-color);
      }
    }
  }

  &:hover {
    .sider-widget__icon-wrap {
      display: block;
    }
  }
}

.radius-icon {
  padding: 3px;
  border-radius: 4px;

  &.active,
  &:hover {
    background-color: var(--workspace-hover-color);
  }
}

.page-delete {
  color: var(--error-color);
}

.search-null {
  color: #b1b3be;
  font-size: 12px;
  height: calc(100% - 280px);
  line-height: 20px;
  padding-top: 192px;
  background-color: transparent;
  text-align: center;
}

.page-delete {
  color: var(--error-color);
}

.search-null.search-null-tree {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b1b3be;
  font-size: 12px;
  height: calc(100% - 138px);
  line-height: 20px;
  background-color: transparent;
}

.ant-divider {
  margin: 13px 0 11px 0 !important;
}

.under_divider {
  margin: 9px 0 9px 0;
}

.ant-tree .ant-tree-node-content-wrapper:active {
  background-color: transparent !important;
}

.menuHandle {
  padding: 8px;
}

.ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: transparent;
}

.sider-widget__page .ant-tree.page-tree .ant-tree-node-selected {
  background-color: transparent;
}

.slider_container {
  background-color: transparent;
  height: calc(100% - 50px);
  overflow: auto;
}

.divider_line {
  margin: 13px 0 !important;
}

.pop_mask {
  z-index: 99 !important;
}

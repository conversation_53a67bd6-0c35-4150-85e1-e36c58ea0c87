import { usePageStore } from '@/store/online-file/use-page';
import { PopoverS, Cell } from '@echronos/editor/dist/core';
import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  LinkOutlined,
  // StarOutlined,
} from '@ant-design/icons';
import Icon from '@/components/icon';
import { useParams, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import confirm from '@/components/modal';
import { Divider, Tooltip } from 'antd';
import React, { useContext, useRef, useState } from 'react';
import { updateSitePage, deleteSitePage } from '@/apis';
import { message } from '@echronos/antd';
import { unsecuredCopyToClipboard } from '@/utils/tools';
import copy from 'copy-to-clipboard';
import PageContext from '../../../../context';
import renamePop from '../rename/rename';
import { TreeNodeData } from '../../model';
import styles from './handle.module.less';

function PageMoreHandle({ nodeData }: { nodeData: TreeNodeData }) {
  const { t } = useTranslation();
  const moreRef = useRef(null as unknown as HTMLElement);
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const { updatePageList, toHome } = useContext(PageContext);
  const { blockId } = useParams();
  const [isShowPop, setIsShowPop] = useState(false);
  const { setPageState } = usePageStore((state) => state);
  const preventPenetrateAndSetNodeDate = (event: React.MouseEvent) => {
    event.stopPropagation();
    setPageState({ ongoingNodeData: nodeData });
    setIsShowPop(true);
  };

  const onCopyLink = () => {
    const url = window.location.href;
    try {
      if (window.isSecureContext && navigator.clipboard) {
        // navigator.clipboard.writeText(url);
        copy(url);
      } else {
        unsecuredCopyToClipboard(url);
      }
      setIsShowPop(false);
      message.success(t('public_copySuccessMsg'));
    } catch (error) {
      message.success(t('public_copyFailMsg'));
    }
  };

  return (
    <PopoverS
      // ref={popover}
      overlayClassName="slide-set-popover"
      placement="bottomLeft"
      trigger="click"
      visible={isShowPop}
      theme="gray"
      content={
        <div
          className={styles.menuHandle}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {!nodeData.attrs.isHomePage && nodeData.publishStatus === 1 && (
            <Cell
              prefix={<Icon name="shop" />}
              hoverBgColor="#fff"
              className={styles.menuItem}
              title={t('build_header_setHome')}
              hasArrow={false}
              hasHover={false}
              onClick={() => {
                updateSitePage({
                  spaceId: nodeData.spaceId,
                  blockId: nodeData.id,
                  attrs: {
                    ...nodeData.attrs,
                    isHomePage: true,
                  },
                  tenantId,
                }).then(() => {
                  message.success(t('build_header_setSuccess'));
                  updatePageList();
                  // popover.current.hidePopover();
                  setIsShowPop(false);
                });
              }}
            />
          )}
          {/* <Cell
            prefix={<StarOutlined />}
            hoverBgColor="#fff"
            title="添加到收藏"
            hasArrow={false}
            hasHover={false}
          /> */}
          <Cell
            prefix={<EditOutlined />}
            hoverBgColor="#fff"
            title={t('build_sider_rename')}
            onClick={() => {
              const rect = moreRef.current?.getBoundingClientRect();
              renamePop().open({
                pos: {
                  top: rect.top,
                  left: rect.left,
                },
                pageName: nodeData.title,
                renameChange: (val) => {
                  updateSitePage({
                    spaceId: nodeData.spaceId,
                    blockId: nodeData.id,
                    attrs: {
                      ...nodeData.attrs,
                      pageName: val,
                    },
                    tenantId: tenantId || '',
                  }).then(() => {
                    updatePageList();
                    message.success(t('build_header_operateSuccessMsg'));
                  });
                },
              });
              // popover.current.hidePopover();
              setIsShowPop(false);
            }}
            hasArrow={false}
            hasHover={false}
          />
          <Cell
            onClick={onCopyLink}
            prefix={<LinkOutlined />}
            hoverBgColor="#fff"
            title={t('build_header_copyLink')}
            hasArrow={false}
            hasHover={false}
          />
          {!nodeData.attrs.isHomePage && (
            <>
              <Divider />
              <Cell
                prefix={<DeleteOutlined />}
                className="page-delete"
                hoverBgColor="#fff"
                title={t('public_delete')}
                onClick={() => {
                  setIsShowPop(false);
                  confirm({
                    title: `${t('public_tip')}`,
                    content: `${t('build_sider_delPageTip')}`,
                    onOk: () => {
                      deleteSitePage({
                        spaceId: nodeData.spaceId,
                        blockId: nodeData.id,
                        tenantId: tenantId || '',
                      }).then(() => {
                        updatePageList();
                        message.success(t('public_deleteSuccessMsg'));
                      });
                      if (nodeData.id === blockId) {
                        toHome();
                      }
                    },
                  });
                  // popover.current.hidePopover();
                  setIsShowPop(false);
                }}
                hasArrow={false}
                hasHover={false}
              />
            </>
          )}
        </div>
      }
    >
      <Tooltip placement="top" title={t('build_header_moreOperate')}>
        <EllipsisOutlined
          ref={moreRef}
          onClick={preventPenetrateAndSetNodeDate}
          className="radius-icon"
        />
      </Tooltip>
    </PopoverS>
  );
}

export default PageMoreHandle;

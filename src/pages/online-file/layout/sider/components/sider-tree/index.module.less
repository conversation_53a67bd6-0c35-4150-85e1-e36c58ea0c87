@import '@echronos/react/less/index';

.siderTree {
  :global {
    .dragging {
      background-color: rgba(0, 140, 255, 0.1) !important;
    }

    .ant-tree-treenode.ant-tree-treenode-switcher-open :hover {
      background-color: transparent;
    }

    .ant-tree {
      background-color: transparent !important;
    }

    .ant-tree .ant-tree-treenode {
      width: 100% !important;
    }

    .ant-tree .ant-tree-node-content-wrapper {
      width: 100% !important;
      padding: 0 0 0 0 !important;
    }

    .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
      background-color: transparent;
    }

    .ant-tree-treenode {
      margin-bottom: 4px;
      padding: 0;
      align-items: center;
      border-radius: 8px;
      z-index: 2;

      .ant-tree-switcher {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 4px;
        z-index: 2;
        opacity: 0;

        svg {
          color: #999eb2;
        }
      }

      &:before {
        bottom: 0;
        border-radius: 8px;
      }

      &:hover {
        background-color: #fff;
      }
      &-selected .ant-tree-indent {
        position: relative;
        &::before {
          content: '';
          display: block;
          width: 2px;
          background-color: #008cff;
          position: absolute;
          top: 11px;
          bottom: 11px;
          left: 0;
        }
      }
      &-selected:not(.dragging span) {
        color: #000;
        background-color: #fff;
        &:before,
        &:hover:before {
          background: #fff;
        }
      }

      &:not(.ant-tree-treenode-selected):hover {
        &:before {
          background-color: var(--workspace-hover-color);
          border-radius: 8px;
        }
      }

      &.progress,
      &:hover {
        .tree-suffix {
          visibility: visible;
        }

        .tree-suffix .anticon,
        .ant-tree-switcher {
          color: #000;
        }
      }
    }
    .ant-tree-draggable-icon {
      display: none;
    }
    .anticon {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      svg:hover {
        background-color: transparent !important;
      }
      &:hover {
        background-color: rgba(#b1b3be, 0.3) !important;
        border-radius: 4px;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
    .treeItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 42px;
      padding-right: 5px;
      border-radius: 8px;
      &:hover .pageHandle {
        display: flex;
        align-items: center;
      }
    }
    .treeItem:hover {
      background-color: #fff !important;
    }

    .pageHandle {
      display: none;
    }

    .pageHandleActive {
      display: flex;
      align-items: center;
    }
  }
}

.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 26px;
  padding: 0 12px 0 8px;
  margin-bottom: 4px;
  font-size: 12px;
  color: rgba(4, 9, 25, 0.7);
  cursor: pointer;
  &:hover .spaceAdd {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.spaceAdd {
  display: none;
  width: 20px;
  height: 20px;
  &:hover {
    background-color: rgba(#b1b3be, 0.3);
    border-radius: 4px;
  }
  border-radius: 4px;
}

.spaceAddIcon {
  font-size: 16px;
  color: #000 !important;
}

.treeItemImg {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  border-radius: 4px;
}

.pageAdd {
  width: 20px;
  height: 20px;
  margin-left: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: rgba(#b1b3be, 0.3) !important;
    border-radius: 4px;
  }
}

@keyframes rotate-90 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

.treeItemIcon {
  position: absolute;
  left: -20px;
  z-index: 999;
  color: #999eb2;

  :global {
    svg {
      color: #999eb2;
    }
  }
}

.treeItemIconRotate {
  animation: rotate-90 0.3s forwards;
}

.treeItemInfo {
  display: flex;
  flex: 1;
}

.treeItemName {
  flex: 1;
  .text-overflow(1);
  word-break: break-word;
}

.homePageTag {
  padding: 3px 5px;
  border-radius: 2px;
  color: #722ed1;
  background-color: #f5e8ff;
  margin-left: 5px;
}

.siderTreeShare {
  :global {
    .ant-tree-switcher {
      opacity: 1 !important;

      svg {
        color: #999eb2;
      }
    }

    .ant-tree-switcher_close .ant-tree-switcher-icon svg {
      transform: rotate(0) !important;
    }

    .ant-tree-switcher_open .ant-tree-switcher-icon svg {
      transform: rotate(90deg) !important;
    }
  }
}

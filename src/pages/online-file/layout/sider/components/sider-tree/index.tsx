import React, {
  Key,
  useCallback,
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { observer } from 'mobx-react-lite';
import { Tree, message } from '@echronos/antd';
import { cloneDeep } from 'lodash';
import { closeSendCommands } from '@echronos/editor/dist/core';
import { space } from '@/store';
import useDocState from '@/store/online-file/use-doc';
import { Tooltip, TreeDataNode } from 'antd';
import { RightOutlined, PlusOutlined } from '@echronos/icons';
import Icon from '@echronos/echos-icon';
import classNames from 'classnames';
import { findParent, updateTree } from '@/utils/handle';
import type { SpaceListType } from '@/apis/site-manager/get-space-list';
import { SpaceMoveType, updateMoveSpace } from '@/apis';
import getSpaceFirstPages from '@/apis/site-manager/get-space-first-pages';
import getPageChildPages from '@/apis/site-manager/get-page-child-page';
import updateMovePage from '@/apis/site-manager/update-move-page';
import createSpaceFirstPage from '@/apis/site-manager/create-space-first-page';
import createPageChildPage from '@/apis/site-manager/create-page-child-page';
import { generateUniqueId } from '@/utils/tools';
import { SpaceMenu, PageMenu } from '../../containers/index';
import styles from './index.module.less';

const defaultPagePng = 'https://img.huahuabiz.com/user_files/2024625/1719301693061916.png';
const defaultIcon = 'https://img.huahuabiz.com/user_files/202479/1720496840222857.png';

export interface SilderTreeInstance {
  onUpdateSpace: (value?: boolean) => void;
}

interface SiderTreeProps {
  type: string;
  treeData: SpaceListType[];
  onChangeData?: (arr: SpaceListType[]) => void;
  onRefresh?: () => void;
  onUpadteTree?: () => void;
}

const SiderTree = forwardRef<SilderTreeInstance, SiderTreeProps>(
  ({ type, treeData, onChangeData, onUpadteTree, onRefresh }, ref) => {
    const navigate = useNavigate();
    const { blockId } = useParams();
    const [searchParams] = useSearchParams();
    const typeParam = searchParams.get('type') || '';
    const webSiteId = searchParams.get('webSiteId') || '';
    const tenantId = searchParams.get('tenantId') || '';
    const updateSider = searchParams.get('updateSider') || '';
    const [spaceList, setSpaceList] = useState<SpaceListType[]>([...treeData]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [expandedKeysShare, setExpandedKeysShare] = useState<string[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [currentHandleKey, setCurrentHandleKey] = useState('');

    const { socketProvider, setSocketProvider } = useDocState();
    const { t } = useTranslation();

    const updateTreeData = useCallback(
      (list: any[], key: React.Key, children: any[]): any[] =>
        list.map((node) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          }
          if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        }),
      []
    );

    // 缓存页面信息
    const onSetPageList = (val: any) => {
      space.setPageInfo(val);
      localStorage.setItem('SITE_PAGE_INFO', JSON.stringify(val));
    };

    /**
     * @description 更新
     * @param treeNode 要更新的节点
     * @param keys 展开的keys
     * @param isFirst 是否默认选择第一个
     */
    const onLoadData = useCallback(
      (treeNode: any, isNextChild = false, keys = [], isFirst = false) => {
        const { spaceId, blockId } = treeNode;
        const pageType = treeNode.type;
        const fn = pageType === 'space' ? getSpaceFirstPages : getPageChildPages;
        const params = pageType === 'space' ? { spaceId, tenantId } : { blockId, tenantId };
        const id = pageType === 'space' ? spaceId : blockId;
        fn(params as any).then((res) => {
          res.list.forEach((item) => {
            const items = item;
            items.name = items.attrs?.pageName || '';
            items.logo = items.attrs?.logo || '';
            items.key = item.blockId;
            items.type = 'page';
          });
          if (res.list && res.list.length) {
            // const pageArr = type === 'group' ? space.spaceGroupList : space.spaceShareList;
            const arr = updateTreeData(spaceList, id, [...res.list]);
            setSpaceList(arr);
            onChangeData?.(arr);
            if (isNextChild) {
              let info;
              if (isFirst) {
                info = res.list[0];
              } else {
                info = res.list[res.list.length - 1];
              }
              onSetPageList(info);
              setSelectedKeys([info.blockId]);
              navigate(`/file/${info.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
                replace: true,
              });
              localStorage.removeItem('SELECT_NODE');
            }
            if (keys && keys.length) {
              setExpandedKeys(cloneDeep(keys));
            }
          }
        });
      },
      [navigate, onChangeData, spaceList, tenantId, updateTreeData, webSiteId]
    );

    // 创建页面
    const onClickPage = (val: SpaceListType) => {
      // type: space在空间下创建一级页面，page在页面下创建页面子集
      // spaceId: 空间id
      // pageBlockId: 页面id
      // localStorage.setItem('SELECT_NODE', JSON.stringify(val));
      // navigate(
      //   `template-market?webSiteId=${webSiteId}&tenantId=${tenantId}&type=${val.type}&spaceId=${val.spaceId}&pageBlockId=${val.blockId}`
      // );

      const fn = val.type === 'space' ? createSpaceFirstPage : createPageChildPage;
      const defaultParams = {
        spaceId: val.spaceId,
        attrs: { pageName: `${t('build_custom_noName')}` },
      };
      const blockId = generateUniqueId();
      const firstBlockId = generateUniqueId();
      const firstBlock = {
        block: [
          {
            attrs: {
              blockId: firstBlockId,
              class: 'node-selectable',
              content: [],
              fullWidth: 'false',
              lineHeight: 24,
              padding: '0',
              margin: '0',
              parentId: '',
              rootBlockId: '',
              textAlign: 'left',
            },
            blockId: firstBlockId,
            content: [],
            parentId: blockId,
            type: 'paragraph',
          },
        ],
        content: [firstBlockId],
      };
      const params =
        val.type === 'space'
          ? {
              blockId,
              tenantId,
              ...firstBlock,
              ...defaultParams,
            }
          : {
              tenantId,
              newBlockId: blockId,
              spaceId: val.spaceId,
              attrs: { pageName: `${t('build_custom_noName')}` },
              blockId: val.blockId,
              ...firstBlock,
            };
      fn({ ...(params as any) }).then(() => {
        message.success(t('build_sider_createSuccessMsg'));
        if (type === 'group') {
          onLoadData(val, true, [...expandedKeys, val.key]);
        } else if (type === 'share') {
          onRefresh?.();
        }
      });
    };

    const findBlockById: any = (array: any, targetId: string) => {
      // eslint-disable-next-line no-restricted-syntax
      for (const item of array) {
        if (item.blockId === targetId || item.spaceId === targetId) {
          return item;
        }
        if (item.children && Array.isArray(item.children)) {
          const found = findBlockById(item.children, targetId);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };

    const getChildKeys = (val: any) => {
      const arr: any = [];
      if (val.children && val.children.length) {
        val.children.forEach((item: any) => {
          arr.push(item.key);
          if (item.children && item.children.length) {
            getChildKeys(item.children);
          }
        });
      }
      return arr;
    };

    // 移动拖拽
    const onDrop = async (info: any) => {
      const sourceList = cloneDeep(spaceList);
      const sourceInfo = cloneDeep(info.dragNode);
      const dropKey = info.node.key;
      const dragKey = info.dragNode.key;
      const dropPos = info.node.pos.split('-');
      const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
      /** 存储拖拽后的空间id和序号 */
      const moveSpaceList: SpaceMoveType = [];
      // 限制拖动页面与空间同级
      if (sourceInfo.type === 'page') {
        if (dropPosition !== 0 && dropPos.length === 2) {
          return;
        }
      }
      // 限制拖动空间与页面同级 空间内进行拖动
      if (sourceInfo.type === 'space') {
        if (
          (dropPosition === 0 && dropPos.length >= 2) ||
          (dropPosition !== 0 && dropPos.length >= 3)
        ) {
          return;
        }
      }
      const loop = (
        data: TreeDataNode[],
        key: Key,
        callback: (node: TreeDataNode, i: number, data: TreeDataNode[]) => void
      ) => {
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < data.length; i++) {
          if (data[i].key === key) {
            return callback(data[i], i, data);
          }
          if (data[i].children) {
            loop(data[i].children!, key, callback);
          }
        }
      };
      const arr = cloneDeep(spaceList);
      const data = [...arr];
      let dragObj: TreeDataNode;
      loop(data, dragKey, (item, index, arr) => {
        arr.splice(index, 1);
        dragObj = item;
      });
      if (!info.dropToGap) {
        loop(data, dropKey, (item) => {
          // eslint-disable-next-line no-param-reassign
          item.children = item.children || [];
          item.children.unshift(dragObj);
        });
      } else {
        let ar: TreeDataNode[] = [];
        let i: number;
        loop(data, dropKey, (_item, index, arr) => {
          ar = arr;
          i = index;
        });
        if (dropPosition === -1) {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          ar.splice(i!, 0, dragObj!);
        } else {
          ar.splice(i! + 1, 0, dragObj!);
        }
      }
      const keys = getChildKeys(info.dragNode);
      if (keys && keys.length) {
        setExpandedKeys(expandedKeys.filter((item) => !keys.includes(item)));
      }
      const result: any = [];
      findParent(cloneDeep(data), sourceInfo.key, result);
      if (result.length > 1 && sourceInfo.type === 'page') {
        const targetInfo = result[result.length - 2];
        updateTree(data, 'key', sourceInfo.key, 'spaceId', targetInfo.spaceId);
        updateTree(data, 'key', sourceInfo.key, 'parentId', targetInfo.key);
        const targetContent = targetInfo.children.map((item: any) => item.key);
        await updateMovePage({
          blockId: sourceInfo.blockId,
          sourceParentBlockId: sourceInfo.parentId,
          sourceParentType: findBlockById(sourceList, info.dragNode.parentId).type,
          targeParentType: targetInfo.type,
          targetParentBlockId: targetInfo.key,
          targetParentContent: targetContent,
          tenantId,
        });
      }
      if (sourceInfo.type === 'space') {
        data.forEach((item, index) => {
          moveSpaceList.push({ sort: index, spaceId: item.spaceId });
        });
        // 调用移动空间接口
        await updateMoveSpace(moveSpaceList);
      }
      setSpaceList(data);
      onChangeData?.(data);
    };

    const findChild = (arr: any, result: any) => {
      if (arr && arr.length) {
        arr.forEach((item: any) => {
          result.push(item.key);
          if (item.children && item.children.length) {
            findChild(item.children, result);
          }
        });
      }
    };

    // 展开收起
    const onSelectExpanded = useCallback(
      (val: SpaceListType, isRequest = false) => {
        if (val.type === 'space' || isRequest) {
          if (!expandedKeys.includes(val.key)) {
            expandedKeys.push(val.key);
            onLoadData(val, false, expandedKeys);
          } else {
            const arr = expandedKeys.filter((item) => item !== val.key);
            const result: any = [];
            findChild(cloneDeep(val.children), result);
            const arrVal = arr.filter((item) => !result.includes(item));
            setExpandedKeys(cloneDeep(arrVal));
          }
        }
      },
      [expandedKeys, onLoadData] // eslint-disable-line
    );

    // 选中节点
    const onSelectNode = (node: SpaceListType) => {
      if (node.type === 'page' && blockId !== node.blockId) {
        onSetPageList(node);
        if (socketProvider) {
          socketProvider.isInitiativeCloseStatus = true;
          socketProvider.destroy();
          setSocketProvider(null);
        }
        closeSendCommands();
        navigate(`/file/${node.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
          replace: true,
        });
        setSelectedKeys([node.key]);
      }
    };

    const onUpdateSpace = useCallback(
      (isOpenFirst) => {
        const info = spaceList[0];
        onLoadData(info, true, [spaceList[0].key], isOpenFirst);
      },
      [onLoadData, spaceList]
    );

    useImperativeHandle(
      ref,
      () => ({
        onUpdateSpace,
      }),
      [onUpdateSpace]
    );

    useEffect(() => {
      setSpaceList([...treeData]);
    }, [treeData]);

    useEffect(() => {
      if (blockId && (!expandedKeys || !expandedKeys.length) && typeParam === 'group') {
        if (spaceList && spaceList.length) {
          const info = spaceList.find((item) => item.hasChild);
          if (info && info.key && info.children && info.children.length) {
            setExpandedKeys([info.key]);
          }
        }
      }
    }, [blockId, spaceList]); // eslint-disable-line

    useEffect(() => {
      setSelectedKeys([blockId || '']);
    }, [blockId]);

    useEffect(() => {
      if (type === 'group') {
        setSpaceList([...space.spaceGroupList]);
      } else {
        setSpaceList([...space.spaceShareList]);
      }
    }, [space.spaceGroupList, space.spaceShareList, type]); // eslint-disable-line

    useEffect(() => {
      // 这里主要用于创建页面以及子页面跳转到模板广场，点击自定义创建用来更新空间列表
      const selectNode = JSON.parse(localStorage.getItem('SELECT_NODE') || '{}');
      if (updateSider === '1' && selectNode?.spaceId) {
        onLoadData(selectNode, true, [...expandedKeys, selectNode.key]);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [updateSider]);

    const titleRender = useCallback(
      (nodeData) => {
        return (
          <div className="treeItem" onClick={() => onSelectExpanded(nodeData)}>
            {(nodeData.hasChild || (nodeData.children && !!nodeData.children.length)) &&
              type === 'group' && (
                <RightOutlined
                  className={classNames(styles.treeItemIcon, {
                    [styles.treeItemIconRotate]: expandedKeys.includes(nodeData.key),
                  })}
                  onClick={(event) => {
                    event.stopPropagation();
                    onSelectExpanded(nodeData, true);
                  }}
                />
              )}
            <div className={styles.treeItemInfo}>
              <img
                className={styles.treeItemImg}
                src={
                  nodeData?.logo ||
                  nodeData?.pageLogo ||
                  (nodeData.type === 'space' ? defaultIcon : defaultPagePng)
                }
                alt=""
              />
              <div
                className={styles.treeItemName}
                title={nodeData.name || `${t('build_custom_noName')}`}
              >
                {nodeData.name || `${t('build_custom_noName')}`}
                {nodeData?.attrs?.isHomePage && (
                  <span className={styles.homePageTag}>{t('build_sider_home')}</span>
                )}
              </div>
            </div>
            <div
              className={classNames(
                'pageHandle',
                currentHandleKey === nodeData.key ? 'pageHandleActive' : ''
              )}
            >
              {nodeData.type === 'space' && (
                <SpaceMenu
                  memberRoleId={nodeData.id}
                  blockId={blockId || ''}
                  spaceId={nodeData.spaceId}
                  spaceInfo={nodeData as SpaceListType}
                  onCloseHandle={() => setCurrentHandleKey('')}
                  webSiteId={webSiteId}
                  tenantId={tenantId}
                >
                  <div className={styles.pageAdd} onClick={() => setCurrentHandleKey(nodeData.key)}>
                    <Icon size={18} name="more_line" className={styles.spaceAddIcon} />
                  </div>
                </SpaceMenu>
              )}
              {nodeData.type !== 'space' && (
                <PageMenu
                  // blockId={nodeData.blockId}
                  webSiteId={webSiteId}
                  tenantId={tenantId}
                  spaceId={nodeData.spaceId}
                  spaceInfo={nodeData as SpaceListType}
                  logoUrl={nodeData.logo}
                  type={type}
                  onCloseHandle={() => setCurrentHandleKey('')}
                  onUpadteTree={() => {
                    // 设置首页后获取最新的数据列表，将空间列表
                    onUpadteTree?.();
                    setExpandedKeys([]);
                  }}
                  spaceList={spaceList}
                  onLoadData={onLoadData}
                >
                  <div className={styles.pageAdd} onClick={() => setCurrentHandleKey(nodeData.key)}>
                    <Icon size={18} name="more_line" className={styles.spaceAddIcon} />
                  </div>
                </PageMenu>
              )}
              <Tooltip
                placement="bottom"
                title={
                  nodeData.type === 'space'
                    ? `${t('build_sider_createPage')}`
                    : `${t('build_sider_createSonPage')}`
                }
              >
                <div className={styles.pageAdd}>
                  <PlusOutlined
                    className={styles.spaceAddIcon}
                    onClick={(event) => {
                      event.stopPropagation();
                      onClickPage(nodeData);
                    }}
                  />
                </div>
              </Tooltip>
            </div>
          </div>
        );
      },
      [blockId, expandedKeys, onSelectExpanded, type, currentHandleKey] // eslint-disable-line
    );

    return (
      <div className={classNames(styles.siderTree, { [styles.siderTreeShare]: type === 'share' })}>
        {type === 'group' && (
          <Tree
            onDrop={onDrop}
            draggable
            treeData={spaceList}
            titleRender={titleRender}
            expandedKeys={expandedKeys}
            onSelect={(selectedKeys, { node }: any) => {
              onSelectNode(node);
            }}
            onExpand={(expandedKey, { expanded, node }) => {
              if (expanded) {
                onLoadData(node);
              }
              setExpandedKeys([...expandedKey] as string[]);
            }}
            selectedKeys={selectedKeys}
          />
        )}
        {type === 'share' && (
          <Tree
            switcherIcon={<RightOutlined />}
            treeData={spaceList}
            titleRender={titleRender}
            onSelect={(selectedKeys, { node }: any) => {
              onSelectNode(node);
            }}
            selectedKeys={selectedKeys}
            expandedKeys={expandedKeysShare}
            onExpand={(expandedKey) => {
              setExpandedKeysShare([...expandedKey] as string[]);
            }}
          />
        )}
      </div>
    );
  }
);

SiderTree.defaultProps = {
  onChangeData: () => {},
  onRefresh: () => {},
  onUpadteTree: () => {},
};

export default observer(SiderTree);

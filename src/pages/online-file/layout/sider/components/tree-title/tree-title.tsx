import { usePageStore } from '@/store/online-file/use-page';
import { useTranslation } from 'react-i18next';
import { Col, Row, Space } from 'antd';
import classNames from 'classnames';
// import { PlusOutlined } from '@echronos/icons';
import MoreHandle from '../handle/handle';
import { TreeNodeData } from '../../model';
import './style.less';

interface TreeTitleProps {
  nodeData: TreeNodeData;
  keyWord: string;
}

function TreeTitle({ nodeData, keyWord }: TreeTitleProps) {
  const { t } = useTranslation();
  const { title = '', id, attrs } = nodeData;

  const { ongoingNodeData } = usePageStore((state) => state);

  // 匹配搜索高亮显示
  const matchSearchValue = () => {
    return title.replaceAll(
      keyWord,
      `<span style="color: var(--workspace-primary-color)">${keyWord}</span>`
    );
  };

  return (
    <Row
      id={id}
      key={id}
      className={classNames('tree-row', { progress: id && id === ongoingNodeData.id })}
      justify="start"
    >
      <Col flex="24px" className="tree-prefix">
        <img
          className="icon"
          src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/20231125/1700900624917180.png"
          alt=""
        />
      </Col>
      <Col flex="1" className="tree-title">
        <p className="ellipsis" dangerouslySetInnerHTML={{ __html: matchSearchValue() }} />
        {attrs.isHomePage && (
          <img
            className="index-icon"
            src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/20231113/1699863313306701.png"
            alt={t('build_sider_home')}
          />
        )}
      </Col>
      <Col className="tree-suffix">
        <Space size={6}>
          <MoreHandle nodeData={nodeData} />
          {/* <Tooltip placement="top" title="新建页面">
            <PlusOutlined className="radius-icon" />
          </Tooltip> */}
        </Space>
      </Col>
    </Row>
  );
}

export default TreeTitle;

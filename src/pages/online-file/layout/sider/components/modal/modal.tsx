import { PropsWithChildren, ReactNode, useEffect, useState } from 'react';
import i18n from 'i18next';
import { Modal, ModalProps, ModalFuncProps, ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import classNames from 'classnames';
import ReactDOM from 'react-dom';
import styles from './modal.module.less';

function EchModal({ children, wrapClassName, ...props }: PropsWithChildren<ModalProps>) {
  const classes = classNames(styles.echModal, wrapClassName);
  return (
    <Modal width={700} centered {...props} wrapClassName={classes}>
      {children}
    </Modal>
  );
}

EchModal.displayName = 'EchModal';

interface EchModalConfig extends ModalProps {
  content?: ReactNode;
}

EchModal.confirm = function confirmFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  return Modal.confirm({
    width: 312,
    cancelText: `${i18n.t('public_cancel')}`,
    okText: `${i18n.t('public_confirm')}`,
    ...props,
    className: classes,
  });
};

EchModal.info = function infoFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  return Modal.info({
    width: 311,
    ...props,
    className: classes,
  });
};

function EchModalBox(props: EchModalConfig) {
  const { content, visible } = props;
  const [isVisible, setVisible] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setVisible(visible as boolean);
    });
  }, []); // eslint-disable-line

  return (
    <ConfigProvider locale={zhCN}>
      <EchModal {...props} visible={isVisible}>
        {content}
      </EchModal>
    </ConfigProvider>
  );
}

EchModal.getInstance = () => {
  const container = document.createDocumentFragment();

  function render(config: EchModalConfig) {
    ReactDOM.render(
      <EchModalBox
        visible
        onCancel={() => {
          close(); // eslint-disable-line
        }}
        {...config}
      >
        {config.content}
      </EchModalBox>,
      container
    );
  }

  function open(config: EchModalConfig) {
    render({ ...config, visible: true });
  }

  function destroy() {
    ReactDOM.unmountComponentAtNode(container);
  }

  function close() {
    render({ visible: false });
    destroy();
  }

  return {
    open,
    close,
  };
};

EchModalBox.defaultProps = {
  content: null,
};

export default EchModal;

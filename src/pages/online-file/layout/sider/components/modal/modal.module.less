.echModalConfirm {
  :global {
    & .ant-modal-content {
      box-shadow: 0 32px 64px 0 rgb(0 0 0 / 22%), 0 2px 21px 0 rgb(0 0 0 / 22%);
      border-radius: 18px;
    }

    & .ant-modal-body {
      padding: 20px;
    }

    & .ant-btn {
      padding: 2px 20px;
      min-height: 28px;
    }

    & [role='img'] {
      display: none;
    }

    & .ant-modal-confirm-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 12px;
    }

    & .ant-modal-confirm-content {
      font-size: 14px;
      line-height: 20px;
      margin-top: 0;
    }

    & .ant-modal-confirm-body > .anticon + .ant-modal-confirm-title + .ant-modal-confirm-content {
      margin-left: 0;
    }

    & .ant-modal-confirm-btns {
      margin-top: 32px;
    }

    & .ant-modal-confirm-btns .ant-btn + .ant-btn {
      margin-left: 16px;
    }

    //& .ant-btn-primary {
    //  background: linear-gradient(108deg, #441eff 1%, #823eff 100%);
    //
    //  &:hover {
    //    background: #823eff;
    //  }
    //}
  }
}

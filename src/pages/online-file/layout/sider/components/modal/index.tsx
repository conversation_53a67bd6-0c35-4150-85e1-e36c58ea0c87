import { Modal, ModalFuncProps } from 'antd';
import i18n from 'i18next';
import classNames from 'classnames';
import styles from './modal.module.less';

const confirm = function confirmFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  return Modal.confirm({
    width: 312,
    centered: true,
    cancelText: `${i18n.t('public_cancel')}`,
    okText: `${i18n.t('public_confirm')}`,
    ...props,
    className: classes,
  });
};

export default confirm;

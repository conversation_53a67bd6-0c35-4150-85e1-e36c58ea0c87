import { PlusOutlined, SearchOutlined, SettingOutlined } from '@echronos/icons';
import { DoubleLeftOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useMemoizedFn, useMount, useRequest } from 'ahooks';
import { Col, Input, Row, Tooltip } from 'antd';
import { cloneDeep, debounce } from 'lodash';
import classNames from 'classnames';
import { useSearchParams, useNavigate, useLocation } from 'react-router-dom';
import { useState, useRef, useEffect, useCallback } from 'react';
import { useSiderStore } from '@/store/online-file/use-sider';
import { checkCharge, checkPermission } from '@echronos/core';
import Icon from '@echronos/echos-icon';
import { space } from '@/store';
import getSearchPage from '@/apis/site-manager/get-search-page';
import getSpaceList, { SpaceListType } from '@/apis/site-manager/get-space-list';
import getSpaceFirstPages from '@/apis/site-manager/get-space-first-pages';
import SiderTree, { SilderTreeInstance } from './components/sider-tree';
import spaceCreate from './containers/space-create';
import './sider.less';

function Sider({
  className,
  slot,
  logo,
  siteName,
}: {
  className: string;
  slot: string;
  logo: string;
  siteName: string;
}) {
  const { collapsed, levitate, setSiderLevitateShow, setSiderLevitateHide, setSiderToggle } =
    useSiderStore((state) => state);

  const { t } = useTranslation();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();

  const isRefreshAll = Number(searchParams.get('isRefreshAll') || 0);
  const webSiteId = searchParams.get('webSiteId') || '';
  const tenantId = searchParams.get('tenantId') || '';

  const groupRef = useRef<SilderTreeInstance>(null as unknown as SilderTreeInstance);

  const [isShowSetting, setIsShowSetting] = useState(false);
  const [searchVal, setSearchVal] = useState('');
  const [spaceList, setSpaceList] = useState<SpaceListType[]>([]);

  // 是否选中设置页
  const isSelectedSettingPage = pathname.includes('/file/settings');
  // 是否选中模板社区
  const isSelectTemplateMarket = pathname.includes('/file/template-market');
  // 是否选中企业模板
  const isSelectTemplateEnterprise = pathname.includes('/file/enterprise-template');

  const testPerm = async (code: string) => {
    if (!checkCharge(code)) {
      return false;
    }
    return checkPermission(code);
  };

  const selectTree = (spaceVal: SpaceListType[]) => {
    const spaceArr = cloneDeep(spaceVal);
    const defaultSpace = spaceArr.find((item) => item.hasChild);
    if (defaultSpace && defaultSpace.key) {
      getSpaceFirstPages({ spaceId: defaultSpace.spaceId, tenantId }).then((res) => {
        res.list.forEach((item) => {
          const items = item;
          items.name = items.attrs?.pageName || '';
          items.logo = items.attrs?.logo || '';
          items.key = item.blockId;
          items.type = 'page';
        });
        if (res.list && res.list.length) {
          spaceArr.forEach((item) => {
            const items = item;
            if (item.key === defaultSpace.key) {
              items.children = res.list as any;
              navigate(`/file/${res.list[0].blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
                replace: true,
              });
            }
          });
          setSpaceList(spaceArr);
          space.setSpaceGroupList(spaceArr);
        }
      });
    }
    // 都没有
    if (!spaceArr || !spaceArr.length) {
      navigate(`template-market?webSiteId=${webSiteId}&tenantId=${tenantId}`);
    }
  };

  // 根据空间数据判断
  const getSpaceNav = (space: SpaceListType[]) => {
    selectTree(space);
  };

  // 获取空间列表
  const runSpace = useCallback((isExpand = true, isOpenFirst = true) => {
    getSpaceList({ tenantId }).then((res) => {
      res.list.forEach((item) => {
        const items = item;
        items.key = item.spaceId;
        items.type = 'space';
      });
      setSpaceList(res.list);
      // 同步到mobx
      space.setSpaceGroupList(res.list);
      if (isExpand) {
        getSpaceNav(res.list);
        groupRef.current.onUpdateSpace(isOpenFirst);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onRecurse = (data: any) => {
    data.forEach((item: any) => {
      const items = item;
      // items.children = item.pageList;
      items.name = item.attrs.pageName;
      items.logo = item.attrs.logo;
      items.key = item.blockId;
      items.type = 'page';
      if (item.children && item.children.length) {
        onRecurse(items.children);
      }
    });
    return data;
  };

  // 获取空间列表
  const { run } = useRequest(getSearchPage, {
    debounceWait: 500,
    manual: true,
    defaultParams: [{ keyWord: searchVal, tenantId }],
    onSuccess: (res) => {
      let group: any = [];
      res.searSpaceList.forEach((item) => {
        const items = item;
        items.name = item.spaceName;
        items.type = 'space';
        items.key = item.spaceId;
        onRecurse(item.pageList);
        items.children = item.pageList;
        items.content = item.children.map((each) => each.blockId);
        group = group.concat(item.pageList);
      });
      setSpaceList(group);
    },
  });

  const onSearch = debounce((val: string) => {
    if (val && val.length) {
      run({ keyWord: val, tenantId });
    } else {
      runSpace(false);
    }
    setSearchVal(val);
  }, 300);

  // 创建新空间
  const createSpace = () => {
    spaceCreate({
      tenantId,
      onConfirm: async () => {
        runSpace();
      },
    });
  };

  // 前往模板广场
  const toTemplateMarket = () => {
    navigate(`template-market?webSiteId=${webSiteId}&tenantId=${tenantId}`);
  };

  // 前往企业模板
  const toEnterpriseTemplate = () => {
    navigate(`enterprise-template?webSiteId=${webSiteId}&tenantId=${tenantId}`);
  };

  // 前往设置页面
  const toSettingPage = useMemoizedFn(() => {
    navigate(`settings/seo?webSiteId=${webSiteId}&tenantId=${tenantId}`);
  });

  const onChangeData = (val: SpaceListType[]) => {
    setSpaceList(val);
  };

  useEffect(() => {
    if (isRefreshAll === 1) {
      runSpace(true, false);
    }
    // 使用模板会走这里
    if (isRefreshAll === 2) {
      runSpace(false);
    }
  }, [isRefreshAll, runSpace]);

  useEffect(() => {
    runSpace(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const arr = spaceList;
    space.setSpaceGroupList(arr);
  }, [searchVal, spaceList]);

  useMount(() => {
    testPerm('BC_001_003_008_001_001').then((res) => {
      setIsShowSetting(res);
    });
  });

  return (
    <div
      slot={slot}
      className={classNames('sider-widget', {
        [`${className}`]: !!className,
        preload: collapsed,
        levitate: collapsed && levitate,
      })}
      onMouseEnter={() => collapsed && setSiderLevitateShow()}
      onMouseLeave={() => collapsed && setSiderLevitateHide()}
    >
      <Row gutter={[8, 0]} className="sider-widget__site chunk-margin">
        <Col>
          <img width={24} height={24} alt="logo" src={logo} />
        </Col>
        <Col>
          <Tooltip title={siteName}>
            <h3 className="sider-widget__name">{siteName}</h3>
          </Tooltip>
        </Col>
        <Col>
          {!levitate && (
            <div className="sider-widget__icon-wrap">
              <Tooltip placement="bottom" title={t('build_sider_putAway')}>
                <DoubleLeftOutlined className="close-icon" onClick={() => setSiderToggle(true)} />
              </Tooltip>
            </div>
          )}
        </Col>
      </Row>
      <div className="sider-widget__chunk">
        <Input
          allowClear
          className="sider-search"
          placeholder={t('public_search')}
          size="large"
          onChange={(e) => {
            onSearch(e.target.value);
          }}
          bordered={false}
          prefix={<SearchOutlined />}
        />
        {!searchVal && isShowSetting && (
          <span
            className={classNames([
              'sider-add-page',
              { 'sider-widget__selected-item': isSelectedSettingPage },
            ])}
            onClick={toSettingPage}
            role="button"
            tabIndex={0}
          >
            <SettingOutlined />
            {t('build_sider_set')}
          </span>
        )}
        <div
          className={classNames([
            'sider-add-page',
            { 'sider-widget__selected-item': isSelectTemplateMarket },
          ])}
          onClick={toTemplateMarket}
          role="button"
          tabIndex={0}
        >
          <Icon name="double_line_arrangement" size={14} style={{ marginRight: '9px' }} />
          {t('build_sider_market')}
        </div>
        <div
          className={classNames([
            'sider-add-page',
            { 'sider-widget__selected-item': isSelectTemplateEnterprise },
          ])}
          onClick={toEnterpriseTemplate}
          role="button"
          tabIndex={0}
        >
          <Icon name="record_line" size={14} style={{ marginRight: '9px' }} />
          {t('build_sider_companyTemp')}
        </div>
      </div>
      <div className="sider-widget__page">
        <div className="category-title">
          {t('build_sider_teamSpace')}
          <Tooltip placement="bottom" title={t('build_sider_createSpace')}>
            <div className="spaceAdd">
              <PlusOutlined
                className="spaceAddIcon"
                onClick={(event) => {
                  event.stopPropagation();
                  createSpace();
                }}
              />
            </div>
          </Tooltip>
        </div>
        <div className="slider_container">
          <SiderTree
            ref={groupRef}
            type="group"
            treeData={spaceList}
            onChangeData={onChangeData}
            onUpadteTree={() => runSpace(false)}
          />
          {!spaceList?.length && !searchVal && (
            <div className="sider-widget__no-data">
              <div className="sider-widget__no-data-text">
                {t('build_sider_noData')}
                <span className="sider-widget__no-data-button" onClick={() => createSpace()}>
                  {t('build_sider_createNow')}
                </span>
              </div>
            </div>
          )}
          {!spaceList?.length && searchVal && (
            <div className="search-null search-null-tree">{t('build_sider_changeWord')}</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Sider;

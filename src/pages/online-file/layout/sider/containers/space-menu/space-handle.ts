import i18n from 'i18next';
import { message } from 'antd';
import { space } from '@/store';
import { removeTree } from '@/utils/handle';
import { cloneDeep } from 'lodash';
import deleteSpace from '@/apis/site-manager/delete-space';
import Modal from '../../components/modal/modal';
/**
 *  @description 删除空间
 */
// eslint-disable-next-line import/prefer-default-export
export function onDeleteSpace(spaceId: string, webSiteId: string, tenantId: string, navigate: any) {
  const pageInfo = cloneDeep(space.pageInfo);
  const spaceList = cloneDeep(space.spaceGroupList);
  Modal.confirm({
    title: `${i18n.t('public_tip')}`,
    icon: '',
    centered: true,
    content: `${i18n.t('build_sider_deleteTip')}`,
    onOk: () => {
      deleteSpace({ spaceId, tenantId }).then(() => {
        message.success(`${i18n.t('public_deleteSuccessMsg')}`);
        const arr = space.spaceGroupList;
        removeTree(arr, spaceId);
        space.setSpaceGroupList(cloneDeep(arr));
        if (!space?.spaceGroupList?.length) {
          return navigate(`template-market?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
            replace: true,
          });
        }
        if (pageInfo.spaceId === spaceId) {
          navigate(`/file/?isRefreshAll=1&webSiteId=${webSiteId}&tenantId=${tenantId}`, {
            replace: true,
          });
        } else {
          const arr = spaceList.filter((item) => item.spaceId !== spaceId);
          space.setSpaceGroupList(cloneDeep(arr));
        }
        // onNavPage({ groupList: arr, shareList: space.spaceShareList,tenantId, navigate });
      });
    },
  });
}

.popover {
  z-index: 10000 !important;
  border-radius: 10px;

  :global {
    .ant-popover-inner {
      background-color: #ecf1f4;
      border-radius: 10px;
    }

    .ant-popover-inner-content {
      padding: 8px !important;
    }

    .ant-popover-arrow {
      display: none !important;
    }
  }
}

.item {
  display: flex;
  align-items: center;
  width: 144px;
  height: 32px;
  padding: 0 8px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;

  &:hover {
    background-color: #fff;
  }
}

.itemIcon {
  margin-right: 8px;
  font-size: 20px !important;
  color: #000;
}

.itemDel,
.itemIconDel {
  color: #ea1c26;
}

.divider {
  padding: 0 8px;

  :global {
    .ant-divider {
      margin: 8px 0 !important;
    }
  }
}

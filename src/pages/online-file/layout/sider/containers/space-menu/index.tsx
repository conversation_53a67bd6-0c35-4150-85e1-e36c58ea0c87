import { ReactNode, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Popover, Divider, Tooltip } from '@echronos/antd';
import classNames from 'classnames';
import { checkPermission } from '@echronos/core';
import { space } from '@/store';
import Icon from '@echronos/echos-icon';
import type { SpaceListType } from '@/apis/site-manager/get-space-list';
import { onDeleteSpace } from './space-handle';
import SelectSpacePagePopup from '../select-space-page-modal';
import styles from './index.module.less';

interface SpaceMenuProps {
  spaceId: string;
  blockId: string;
  memberRoleId: string;
  onCloseHandle: () => void;
  spaceInfo: SpaceListType;
  children: ReactNode;
  webSiteId: string;
  tenantId: string;
}

function SpaceMenu({
  blockId,
  spaceId,
  spaceInfo,
  onCloseHandle,
  webSiteId,
  tenantId,
  ...props
}: SpaceMenuProps) {
  const { t } = useTranslation();
  const menu = [
    {
      key: 'publishTemplate',
      label: `${t('build_sider_publishTemplate')}`,
      icon: 'double_line_arrangement',
    },
    {
      key: 'setup',
      label: `${t('build_sider_spaceSet')}`,
      icon: 'set_line',
    },
  ];
  const navigate = useNavigate();
  const [showPopover, setShowPopover] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const onClickIcon = (e: any) => {
    e.stopPropagation();
    setShowPopover(true);
    setShowTooltip(false);
  };

  const onApaceSet = (even: any, val: string) => {
    even.stopPropagation();
    setShowPopover(false);
    onCloseHandle();
    switch (val) {
      case 'publishTemplate':
        SelectSpacePagePopup({
          businessId: spaceId,
          onConfirm: async () => {
            navigate(
              `template-setting?webSiteId=${webSiteId}&tenantId=${tenantId}&spaceId=${spaceId}&blockId=${blockId}`,
              { replace: true }
            );
          },
        });
        break;
      case 'setup':
        space.setSpaceInfo({ ...spaceInfo, blockId } as any);
        navigate(`space-setup?spaceId=${spaceId}&webSiteId=${webSiteId}&tenantId=${tenantId}`);
        break;
      case 'del':
        onDeleteSpace(spaceId, webSiteId, tenantId, navigate);
        break;
      default:
        break;
    }
  };

  const content = (
    <>
      {menu.map((item) => {
        if (
          item.label === `${t('build_sider_publishTemplate')}` &&
          !checkPermission('BC_001_003_008_002')
        )
          return;
        return (
          <div
            className={styles.item}
            key={item.key}
            onClick={(event) => onApaceSet(event, item.key)}
          >
            <Icon className={styles.itemIcon} name={item.icon} />
            <span>{item.label}</span>
          </div>
        );
      })}
      {spaceInfo.isOwner && (
        <>
          <div className={styles.divider}>
            <Divider />
          </div>
          <div
            className={classNames(styles.item, styles.itemDel)}
            onClick={(event) => onApaceSet(event, 'del')}
          >
            <Icon
              className={classNames(styles.itemIcon, styles.itemIconDel)}
              name="delete_trash_line"
            />
            <span>{t('build_sider_deldetSpace')}</span>
          </div>
        </>
      )}
    </>
  );

  return (
    <div>
      <Popover
        trigger={showPopover ? 'hover' : 'click'}
        placement="bottomLeft"
        overlayClassName={styles.popover}
        content={content}
        visible={showPopover}
        onVisibleChange={(val) => {
          setShowPopover(val);
          if (!val) {
            onCloseHandle();
          }
        }}
      >
        <Tooltip placement="bottom" title={t('build_header_moreOperate')} visible={showTooltip}>
          <div
            onClick={onClickIcon}
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            {props.children}
          </div>
        </Tooltip>
      </Popover>
    </div>
  );
}

export default SpaceMenu;

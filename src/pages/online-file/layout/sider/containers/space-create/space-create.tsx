import { PropsWithChildren, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ModalProps, message } from '@echronos/antd';
import { isFunction, debounce } from 'lodash';
import { generateUniqueId } from '@/utils/tools';
import createSiteSpace from '@/apis/site-manager/create-site-space';
import SpaceBox from '../space-box/index';
import styles from './space-create.module.less';

export interface SpaceCreateProps extends ModalProps {
  tenantId: string;
  onConfirm: () => void;
}

function SpaceCreate({ tenantId, onConfirm, ...props }: PropsWithChildren<SpaceCreateProps>) {
  const { t } = useTranslation();
  const [isDisabled, setIsDisabled] = useState(false);
  const [formValue, setFormValue] = useState({
    logo: '',
    name: '',
    description: '',
  });
  const [showEmoji, setShowEmoji] = useState(false);

  const onSubmit = debounce(() => {
    setIsDisabled(true);
    const blockId = generateUniqueId();
    const plcBlockId = generateUniqueId();
    createSiteSpace({
      ...formValue,
      blockId,
      block: [
        {
          attrs: {
            blockId: plcBlockId,
            class: 'node-selectable',
            content: [],
            fullWidth: 'false',
            lineHeight: 24,
            padding: '0',
            margin: '0',
            parentId: '',
            rootBlockId: '',
            textAlign: 'left',
          },
          blockId: plcBlockId,
          content: [],
          parentId: blockId,
          type: 'paragraph',
        },
      ],
      content: [plcBlockId],
      tenantId,
    })
      .then(() => {
        message.success(t('build_sider_createSuccessMsg'));
        onConfirm();
        // @ts-ignore
        props.onClose();
      })
      .finally(() => {
        setIsDisabled(false);
      });
  }, 500);

  const onClose = () => {
    if (!showEmoji) {
      if (isFunction(props.onCancel)) {
        // @ts-ignore
        props.onCancel();
      }
    }
    setShowEmoji(false);
  };

  return (
    <Modal
      {...props}
      centered
      width={440}
      title={t('build_sider_createSpace')}
      wrapClassName={styles.modal}
      okButtonProps={{ disabled: !formValue.name.length || isDisabled }}
      okText={t('build_sider_create')}
      onOk={onSubmit}
      onCancel={onClose}
      confirmLoading={isDisabled}
    >
      <SpaceBox onChange={setFormValue} onClickEmoji={setShowEmoji} />
    </Modal>
  );
}

export default SpaceCreate;

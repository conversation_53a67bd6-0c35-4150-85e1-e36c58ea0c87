import { ReactNode, useRef, useState, MouseEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { Popover, Divider, message, Modal, Tooltip } from '@echronos/antd';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';
import Icon from '@echronos/echos-icon';
// import { closeSendCommands } from '@echronos/editor/dist/core';
import { useNavigate, useParams } from 'react-router-dom';
import { checkPermission } from '@echronos/core';
// import useDocState from '@/store/online-file/use-doc';
import { space, head } from '@/store';
import { onCopyLink, onNavBlock, removeTree, updateTree } from '@/utils/handle';
import updatePageName from '@/apis/site-manager/update-page-name';
import deletePage from '@/apis/site-manager/delete-page';
import { updateSitePage, CreateCopyPageType, createCopyPage } from '@/apis';
import type { SpaceListType } from '@/apis/site-manager/get-space-list';
import renamePop from '../../components/rename/rename';
import SelectSpacePagePopup from '../select-space-page-modal';
import styles from '../space-menu/index.module.less';

interface PageMenuProps {
  type: string;
  webSiteId: string;
  tenantId: string;
  spaceId: string;
  spaceInfo: SpaceListType;
  children: ReactNode;
  spaceList: SpaceListType[];
  onLoadData: (treeNode: any) => void;
  onCloseHandle: () => void;
  onUpadteTree?: () => void;
}

const defaultPagePng = 'https://img.huahuabiz.com/user_files/2024625/1719301693061916.png';

function PageMenu({
  type,
  webSiteId,
  tenantId,
  spaceId,
  spaceInfo,
  onCloseHandle,
  onUpadteTree,
  onLoadData,
  spaceList,
  ...props
}: PageMenuProps & { logoUrl: string }) {
  const { t } = useTranslation();
  const menu = [
    {
      key: 'setupIndex',
      label: `${t('build_header_setHome')}`,
      icon: 'shop_line',
    },
    {
      key: 'createCopy',
      label: `${t('build_sider_createCopy')}`,
      icon: 'add_line',
    },
    {
      key: 'rename',
      label: `${t('build_sider_rename')}`,
      icon: 'edit_line',
    },
    {
      key: 'copy',
      label: `${t('build_header_copyLink')}`,
      icon: 'copy_line',
    },
    {
      key: 'publishTemplate',
      label: `${t('build_sider_publishTemplate')}`,
      icon: 'double_line_arrangement',
    },
  ];
  const navigate = useNavigate();
  const moreRef = useRef(null);
  const { blockId } = useParams();
  const [showPopover, setShowPopover] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const ev = useRef<MouseEvent>(null);
  // const { indexDBProvider, setIndexDBProvider, socketProvider, setSocketProvider } = useDocState();

  // const params = useParams();

  const onClickIcon = (e: any) => {
    // @ts-expect-error todo
    ev.current = e;
    e.stopPropagation();
    setShowPopover(true);
    setShowTooltip(false);
  };

  // 更新列表
  const onUpdateList = (val: string, name = '', logo = '') => {
    const arr =
      type === 'group' ? cloneDeep(space.spaceGroupList) : cloneDeep(space.spaceShareList);
    if (val === 'del') {
      if (blockId === spaceInfo.blockId) {
        onNavBlock({
          groupList: type === 'group' ? arr : cloneDeep(space.spaceGroupList),
          shareList: type === 'share' ? arr : cloneDeep(space.spaceGroupList),
          blockInfo: spaceInfo,
          navigate,
          type,
          webSiteId,
          tenantId,
        });
      }
      removeTree(arr, spaceInfo.key);
    }
    if (val === 'update') {
      updateTree(arr, 'blockId', spaceInfo.blockId, 'name', name);
      updateTree(arr, 'blockId', spaceInfo.blockId, 'logo', logo);
    }
    if (type === 'group') {
      space.setSpaceGroupList(cloneDeep(arr));
    } else {
      space.setSpaceShareList(cloneDeep(arr));
    }
    // 更新头部导航
    const headerArr = space.headerNavList;
    headerArr.forEach((item) => {
      const items = item;
      if (item.key === spaceInfo.key) {
        items.name = name;
        items.logo = logo || defaultPagePng;
      }
    });
    space.setHeaderNavList(cloneDeep(headerArr));
    localStorage.setItem('HEADER_NAV_LIST', JSON.stringify(cloneDeep(headerArr)));
    if (spaceInfo.blockId === blockId) {
      const headInfo = head.headData;
      head.setHeadData({
        ...headInfo,
        title: name,
        avatar: {
          url: logo,
          type: 'icon',
        },
      });
    }
  };

  // 重命名
  const onRename = () => {
    // @ts-ignore
    const rect = ev.current?.target?.getBoundingClientRect();
    renamePop().open({
      pos: {
        top: rect.top - 90,
        left: rect.left,
      },
      pageName: spaceInfo.name,
      logoUrl: spaceInfo.logo,
      renameChange: (val, logo) => {
        updatePageName({
          tenantId,
          spaceId,
          blockId: spaceInfo.blockId,
          attrs: {
            ...spaceInfo.attrs,
            pageName: val,
            logo,
          },
        }).then(() => {
          message.success(t('build_sider_changeSuccessMsg'));
          onUpdateList('update', val, logo);
          // if (spaceInfo.blockId === blockId) {
          //   // 协同标题
          //   if (socketProvider) {
          //     const headInfo = head.headData;
          //     socketProvider.ws?.send(
          //       JSON.stringify({
          //         type: 'EDIT',
          //         content: {
          //           head: {
          //             ...headInfo,
          //             title: val,
          //             avatar: {
          //               url: logo,
          //               type: 'icon',
          //             },
          //           },
          //           blockId,
          //         },
          //         message: '',
          //       })
          //     );
          //   }
          // }
        });
      },
    });
  };

  // 删除页面
  const onDeletePage = () => {
    Modal.confirm({
      title: `${t('public_tip')}`,
      icon: '',
      centered: true,
      content: `${t('build_sider_delCurrentPageTip')}`,
      onOk: () => {
        deletePage({ spaceId, blockId: spaceInfo.blockId, tenantId }).then(() => {
          message.success(t('public_deleteSuccessMsg'));
          // 关闭 socket 链接并删除本地缓存
          // if (params.blockId === spaceInfo.blockId) {
          //   if (socketProvider) {
          //     socketProvider.isInitiativeCloseStatus = true;
          //     socketProvider.destroy();
          //     setSocketProvider(null);
          //   }
          //   closeSendCommands();
          //   // 删除本地缓存的文档数据
          //   indexDBProvider?.clearData();
          //   setIndexDBProvider(null);
          // } else {
          //   indexedDB.deleteDatabase(`doc-${spaceInfo.blockId}`);
          // }

          onUpdateList('del');
        });
      },
    });
  };

  // 创建副本
  const onCreateCopy = () => {
    const arr = cloneDeep(spaceInfo);
    const params: CreateCopyPageType = {
      pageName: spaceInfo.name,
      spaceId: spaceInfo.spaceId,
      parentId: spaceInfo.parentId,
      blockId: spaceInfo.blockId,
      tenantId,
    };
    createCopyPage(params).then(() => {
      message.success(t('build_header_copySuccessMsg'));
      const parentSpaceInfo = spaceList.find((item) => item.spaceId === spaceInfo.parentId);
      // 及时更新空间 | 页面数据
      if (parentSpaceInfo?.type === 'space') {
        onLoadData(parentSpaceInfo);
      } else {
        const parentPageInfo = { ...arr, blockId: arr.parentId };
        onLoadData(parentPageInfo);
      }
    });
  };

  const onApaceSet = (e: MouseEvent, val: string) => {
    e.stopPropagation();
    setShowPopover(false);
    onCloseHandle();
    switch (val) {
      case 'createCopy':
        onCreateCopy();
        break;
      case 'setupIndex':
        updateSitePage({
          spaceId,
          blockId: spaceInfo.blockId,
          attrs: {
            pageName: spaceInfo.name,
            isHomePage: true,
          },
          tenantId,
        }).then(() => {
          message.success(t('build_header_setSuccess'));
          onUpadteTree?.();
        });
        break;
      case 'rename':
        onRename();
        break;
      case 'copy':
        // 将当前页面id和租户id传入
        onCopyLink();
        break;
      case 'publishTemplate':
        SelectSpacePagePopup({
          businessId: spaceId,
          onConfirm: async () => {
            navigate(
              `template-setting?webSiteId=${webSiteId}&tenantId=${tenantId}&spaceId=${spaceId}&blockId=${spaceInfo.blockId}`,
              { replace: true }
            );
          },
        });
        break;
      case 'del':
        onDeletePage();
        break;
      default:
        break;
    }
  };

  const content = (
    <>
      {menu.map((item) => {
        if (
          item.label === `${t('build_sider_publishTemplate')}` &&
          !checkPermission('BC_001_003_008_002')
        )
          return;
        return (
          <div
            ref={moreRef}
            className={styles.item}
            key={item.key}
            onClick={(event) => onApaceSet(event, item.key)}
          >
            <Icon className={styles.itemIcon} name={item.icon} />
            <span>{item.label}</span>
          </div>
        );
      })}

      <div className={styles.divider}>
        <Divider />
      </div>

      <div
        className={classNames(styles.item, styles.itemDel)}
        onClick={(event) => onApaceSet(event, 'del')}
      >
        <Icon
          className={classNames(styles.itemIcon, styles.itemIconDel)}
          name="delete_trash_line"
        />
        <span>{t('build_sider_delPage')}</span>
      </div>
    </>
  );

  return (
    <Popover
      trigger={showPopover ? 'hover' : 'click'}
      placement="bottomLeft"
      overlayClassName={styles.popover}
      content={content}
      visible={showPopover}
      onVisibleChange={(val) => {
        setShowPopover(val);
        if (!val) {
          onCloseHandle();
        }
      }}
    >
      <Tooltip placement="bottom" title={t('build_header_moreOperate')} visible={showTooltip}>
        <div
          role="button"
          tabIndex={0}
          onClick={onClickIcon}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {props.children}
        </div>
      </Tooltip>
    </Popover>
  );
}

PageMenu.defaultProps = {
  onUpadteTree: () => {},
};

export default PageMenu;

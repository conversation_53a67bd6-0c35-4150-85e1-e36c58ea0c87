import { PropsWithChildren, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalProps, Input } from '@echronos/antd';
import { SelectEmojiPicture } from '@echronos/millet-ui';
import styles from './index.module.less';

interface SpaceFormData {
  logo: string;
  name: string;
  description: string;
}

export interface SpaceCreateProps extends ModalProps {
  defaultValue?: SpaceFormData;
  onChange: (params: SpaceFormData) => void;
  onClickEmoji?: (params: boolean) => void;
  isEdit?: boolean;
}

const defaultIcon = 'https://img.huahuabiz.com/user_files/202479/1720496840222857.png';

function SpaceBox({
  defaultValue,
  onChange,
  onClickEmoji,
  isEdit,
}: PropsWithChildren<SpaceCreateProps>) {
  const { t } = useTranslation();
  const [formValue, setFormValue] = useState({
    logo: defaultIcon,
    name: '',
    description: '',
  });

  const onChangeForm = (val: string, key: string) => {
    const form = {
      ...formValue,
      [key]: val,
    };
    setFormValue({ ...form });
    onChange({ ...form });
    onClickEmoji?.(false);
  };

  const onRemoveIcon = () => {
    const info = {
      ...formValue,
      logo: defaultIcon,
    };
    setFormValue({ ...info });
    onChange({
      ...info,
    });
    onClickEmoji?.(false);
  };

  useEffect(() => {
    if (defaultValue) {
      setFormValue({ ...defaultValue });
    }
  }, [defaultValue]);

  return (
    <div className={styles.spaceBox}>
      <div className={styles.label}>
        {isEdit ? '' : `${t('build_sider_create')}`}
        {t('build_sider_iconAndName')}
      </div>
      <div className={styles.name}>
        <SelectEmojiPicture
          type="emoji"
          onChange={(e) => onChangeForm(e, 'logo')}
          onRemove={onRemoveIcon}
        >
          <div className={styles.icon} onClick={() => onClickEmoji?.(true)}>
            <img className={styles.logo} src={formValue.logo || defaultIcon} alt="" />
          </div>
        </SelectEmojiPicture>
        <Input
          value={formValue.name}
          className={styles.nameInput}
          showCount
          maxLength={15}
          placeholder={t('build_sider_spaceNameInput')}
          onChange={(e) => onChangeForm(e.target.value, 'name')}
        />
      </div>
      <div className={styles.label}>{t('build_sider_desc')}</div>
      <Input.TextArea
        value={formValue.description}
        showCount
        maxLength={200}
        placeholder={t('build_sider_spaceIntro')}
        className={styles.tip}
        onChange={(e) => onChangeForm(e.target.value, 'description')}
      />
    </div>
  );
}

SpaceBox.defaultProps = {
  defaultValue: null,
  onClickEmoji: () => {},
  isEdit: false,
};

export default SpaceBox;

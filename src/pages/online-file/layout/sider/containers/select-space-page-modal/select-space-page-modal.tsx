import { PropsWithChildren, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ModalProps, Tree, message } from '@echronos/antd';
import { isFunction, debounce } from 'lodash';
import getSpacePageList, { TreeChildrenType } from '@/apis/site-manager/get-space-page-list';
import styles from './select-space-page-modal.module.less';

export interface SelectSpacePageModalProps extends ModalProps {
  businessId: string;
  onConfirm: MultipleParamsFn<[value?: string[]]>;
}

function SelectSpacePageModal({
  businessId,
  onConfirm,
  ...props
}: PropsWithChildren<SelectSpacePageModalProps>) {
  const { t } = useTranslation();
  const [spacePageTree, setSpacePageTree] = useState<any>([]);
  const [indexPageList, setIndexPageList] = useState<any>([]);

  // 关闭弹窗
  const onClose = () => {
    if (isFunction(props.onCancel)) {
      // @ts-ignore
      props.onCancel();
    }
  };

  // 确定
  const onOk = debounce(() => {
    if (indexPageList.length) {
      localStorage.setItem('INDEX_PAGE_LIST', JSON.stringify(indexPageList));
      onConfirm();
      onClose();
    } else {
      message.warning(t('build_sider_selectPage'));
    }
  }, 500);

  useEffect(() => {
    function formatChildren(data: TreeChildrenType[]) {
      return data?.map((item: TreeChildrenType) => {
        const newItem = {
          ...item,
          name: item?.attrs.pageName || `${t('build_custom_noName')}`,
          isHomePage: item.attrs.isHomePage,
          isOpen: true,
        };
        // 如果当前项有children，递归处理children
        if (item.children) {
          newItem.children = formatChildren(item.children);
        }

        return newItem;
      });
    }

    getSpacePageList({ spaceId: businessId }).then((res) => {
      setSpacePageTree([
        {
          blockId: res.spaceId,
          name: res.name,
          isSpace: true,
          isOpen: true,
          children: formatChildren(res.children),
        },
      ]);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businessId]);

  return (
    <Modal
      {...props}
      centered
      width={480}
      wrapClassName={styles.cusModal}
      closable={false}
      onOk={onOk}
      onCancel={onClose}
    >
      {spacePageTree.length > 0 ? (
        <Tree
          checkable
          className={styles.tree}
          onCheck={(value, values) => {
            setIndexPageList(values.checkedNodes);
          }}
          treeData={spacePageTree}
          fieldNames={{ title: 'name', key: 'blockId', children: 'children' }}
          defaultExpandAll
        />
      ) : null}
    </Modal>
  );
}

export default SelectSpacePageModal;

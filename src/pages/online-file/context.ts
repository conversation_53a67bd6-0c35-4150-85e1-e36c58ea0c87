import { createContext } from 'react';
import { Editor as TiptapEditor, JSONContent } from '@tiptap/core';

interface Context {
  updatePageList: () => void;
  toHome: () => void;
  currentPageAttrs: Record<string, any>;
  updatePageAttrs: (attrs: Record<string, any>) => void;
  watchUpdate: boolean;
  content: JSONContent;
  updateEditor: (editor: TiptapEditor) => void;
}

const context = createContext<Context>({
  updatePageList: () => {},
  toHome: () => {},
  currentPageAttrs: {},
  updatePageAttrs: () => {},
  watchUpdate: false,
  content: {},
  updateEditor: () => {},
});

export default context;

import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, message } from '@echronos/antd';
import { useSearchParams } from 'react-router-dom';
import { space } from '@/store';
import { updateTree } from '@/utils/handle';
import { cloneDeep } from 'lodash';
import getSpaceDetails from '@/apis/site-manager/get-space-details';
import updateSpaceInfo from '@/apis/site-manager/update-space-info';
import SpaceBox from '../layout/sider/containers/space-box';
import styles from './index.module.less';

const initData = {
  logo: '',
  name: '',
  description: '',
};

function SpaceSetup() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const spaceId = searchParams.get('spaceId') || '';
  const tenantId = searchParams.get('tenantId') || '';
  const [formValue, setFormValue] = useState(initData);
  const [formValueState, setFormValueState] = useState(initData);

  // 保存空间设置
  const onSubmit = () => {
    updateSpaceInfo({ ...formValue, spaceId, tenantId } as any).then(() => {
      setFormValueState(cloneDeep(formValue));
      message.success(t('public_save_successMsg'));
      const arr = space.spaceGroupList;
      updateTree(arr, 'spaceId', spaceId, 'name', formValue.name);
      updateTree(arr, 'spaceId', spaceId, 'logo', formValue.logo);
      space.setSpaceGroupList(cloneDeep(arr));
      const spaceInfo = {
        ...space.spaceInfo,
        name: formValue.name,
        logo: formValue.logo || 'https://img.huahuabiz.com/user_files/202479/1720496840222857.png',
        description: formValue.description,
      };
      space.setSpaceInfo({ ...spaceInfo });
      localStorage.setItem('SITE_SPACE_INFO', JSON.stringify(spaceInfo));
    });
  };

  const isDisabled = useMemo(() => {
    return JSON.stringify(formValueState) === JSON.stringify(formValue) || !formValue.name;
  }, [formValue, formValueState]);

  useEffect(() => {
    getSpaceDetails({ spaceId, tenantId }).then((res) => {
      const info = {
        logo: res.logo || 'https://img.huahuabiz.com/user_files/202479/1720496840222857.png',
        name: res.name,
        description: res.description,
      };
      setFormValueState(cloneDeep({ ...info }));
      setFormValue(cloneDeep({ ...info }));
    });
  }, [spaceId, tenantId]);

  return (
    <div className={styles.setupBox}>
      <div className={styles.leftBox}>
        <div className={styles.modalTitle}>{t('build_sider_set')}</div>
        <div className={styles.tab}>
          <div className={styles.line} />
          {t('build_space_baseInfo')}
        </div>
      </div>
      <div className={styles.rightBox}>
        <SpaceBox defaultValue={formValue} onChange={setFormValue} isEdit />
        <Button type="primary" disabled={isDisabled} className={styles.button} onClick={onSubmit}>
          {t('public_save')}
        </Button>
      </div>
    </div>
  );
}

export default SpaceSetup;

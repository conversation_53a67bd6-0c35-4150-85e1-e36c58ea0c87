import { Editor as TiptapEditor } from '@tiptap/core';
import { useContext, useMemo } from 'react';
import FloatNavigat from '@echronos/editor/dist/node-float-navigat';
import HotZoneNode from '@echronos/editor/dist/node-hot-zone';
import Editor, { BubbleFormatQuickBar, useSiteInfo } from '@echronos/editor/dist/core';
import { LogoHandle } from '@echronos/editor/dist/node-logo';
import { PanelHandle } from '@echronos/editor/dist/node-panel-group';

import extensionsConfig from '../extensions';
import context from '../context';
import './index.less';

export default function Workspaces() {
  const { currentPageAttrs, updatePageAttrs, watchUpdate, content, updateEditor } =
    useContext(context);

  // 站点域名
  const domain = useSiteInfo((state) => state.domain);

  const onEditorCreate = (e: TiptapEditor) => {
    updateEditor(e);
  };

  const editorStyle = useMemo(() => {
    if (currentPageAttrs.backgroundImage) {
      return {
        background: `url(${currentPageAttrs.backgroundImage})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100% auto',
      };
    }
    if (currentPageAttrs.backgroundColor) {
      return {
        backgroundColor: currentPageAttrs.backgroundColor,
      };
    }
    return {};
  }, [currentPageAttrs]);

  const updateFloatNavigatAttr = (key: string, value: any) => {
    const attrs = {
      ...currentPageAttrs,
    };
    attrs.floatNav[key] = value;
    updatePageAttrs(attrs);
  };

  const deleteFloatNavigatAttr = () => {
    const attrs = {
      ...currentPageAttrs,
    };
    attrs.floatNav = {};
    updatePageAttrs(attrs);
  };

  return content && domain ? (
    <Editor
      watchUpdate={watchUpdate}
      extensionsConfig={[
        ...extensionsConfig,
        HotZoneNode.configure({
          siteDomain: domain,
        }),
      ]}
      content={content}
      onCreate={onEditorCreate}
      style={{ padding: '0 10px', ...editorStyle }}
      handlesConfig={[PanelHandle, LogoHandle]}
    >
      {/* 快捷菜单 */}
      <BubbleFormatQuickBar
        config={[
          'TransformTextTag',
          'TransformFontFamily',
          'TransformFontSize',
          'TransformFontWeight',
          'Space',
          'TransformTextColor',
          'TransformSpacing',
          'TransformTextAlign',
          'FlexSpacing',
          'More',
        ]}
      />

      {/* 自定义悬浮菜单 */}
      {currentPageAttrs.floatNav && currentPageAttrs.floatNav.navigatList && (
        <FloatNavigat
          navigatList={currentPageAttrs.floatNav.navigatList}
          position={currentPageAttrs.floatNav.position}
          bgColor={currentPageAttrs.floatNav.bgColor}
          showTitle={currentPageAttrs.floatNav.showTitle}
          updateAttrs={updateFloatNavigatAttr}
          deleteNode={deleteFloatNavigatAttr}
        />
      )}
    </Editor>
  ) : null;
}

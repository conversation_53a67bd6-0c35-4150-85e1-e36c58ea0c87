import { useTranslation } from 'react-i18next';
import { useSiderStore } from '@/store/online-file/use-sider';
import {
  editorProviderParentId,
  setPageData,
  getPageData,
  setEvent,
  useSiteInfo,
} from '@echronos/editor/dist/core';
import { message } from '@echronos/antd';
import { generateUniqueId } from '@/utils/tools';
import { Editor as TiptapEditor, JSONContent } from '@tiptap/core';
import NiceModal from '@ebay/nice-modal-react';
import Page from '@/components/page';
import { getSitePages, getPageBlocks, getSiteDetail, SitePage, updateSitePage } from '@/apis';
import { useEffect, useState, useMemo, useCallback, useRef, Suspense } from 'react';
import { useParams, useSearchParams, useNavigate, Outlet } from 'react-router-dom';
import { transformProtocols } from '@/utils/utils';
import { Spin } from 'antd';
import PageContext from './context';
import Layout from './layout/index';
import Sider from './layout/sider/sider';
import Header from './layout/header/header';
import style from './style.module.less';
import './antd.reset.less';

function OnlineFile() {
  const { t } = useTranslation();
  const { collapsed } = useSiderStore((state) => state);
  const { blockId } = useParams();
  const [content, setContent] = useState(null as unknown as JSONContent);
  const [watchUpdate, setWatchUpdate] = useState(false);
  const [editor, setEditor] = useState<TiptapEditor>(null as unknown as TiptapEditor);
  const [currentPageAttrs, setCurrentPageAttrs] = useState<Record<string, any>>({});
  const [pageList, setPageList] = useState<SitePage[]>([]);
  const navigate = useNavigate();
  const mainElement = useRef(null as unknown as HTMLElement);

  // 网站信息
  const { setSiteInfo, logo, name } = useSiteInfo((state) => state);

  const [searchParams] = useSearchParams();
  const webSiteId = searchParams.get('webSiteId');
  const tenantId = searchParams.get('tenantId') || '';

  const getPageList = useCallback(() => {
    getSitePages({ keyWord: '', tenantId }).then((res) => {
      const list = res.list[0].pageList;
      setPageList(list);
      const page = list.filter((item) => {
        return item.blockId === blockId;
      });
      if (page && page.length > 0) {
        setCurrentPageAttrs({
          ...page[0].attrs,
          blockId: page[0].blockId,
          spaceId: page[0].spaceId,
        });
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setPageList, setCurrentPageAttrs, blockId]);

  useEffect(() => {
    if (!webSiteId) return;
    getSiteDetail(webSiteId || '').then(
      (res: {
        id: number;
        logo: string;
        domain: string;
        tenantName: string;
        companyId: number;
      }) => {
        setSiteInfo({
          id: res.id,
          logo: res.logo,
          domain: res.domain,
          name: res.tenantName,
          companyId: res.companyId,
        });
      }
    );
  }, [webSiteId, setSiteInfo]);

  useEffect(() => {
    setWatchUpdate(false);
    if (blockId) {
      getPageBlocks({
        blockId,
        tenantId,
      }).then((res) => {
        let jsonContent = {} as JSONContent;
        jsonContent = transformProtocols(res, jsonContent);
        // eslint-disable-next-line no-console
        console.log('json---', jsonContent);
        setContent(jsonContent);
        setPageData({
          data: res,
          spaceId: jsonContent.spaceId,
          pageId: (jsonContent.attrs && jsonContent.attrs.blockId) || '',
          publishStatus: res.publishStatus || 0,
        });
        if (!mainElement.current) {
          mainElement.current = document.querySelector('.workspace-main__body') as HTMLElement;
        }
        mainElement.current.scrollTop = 0;
        setCurrentPageAttrs({
          ...res.attrs,
          blockId: jsonContent.attrs ? jsonContent.attrs.blockId : '',
          spaceId: jsonContent.spaceId,
        });
        setWatchUpdate(true);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockId]);

  const updatePageAttrs = useCallback(
    (attrs: Record<string, any>) => {
      setCurrentPageAttrs(attrs);
      const pageData = getPageData();
      const json = editor.getJSON();
      const contentIds: string[] = [];
      if (json?.content && json?.content.length > 0) {
        json?.content.forEach((it) => {
          if (it?.attrs?.blockId) {
            contentIds.push(it.attrs.blockId);
          }
        });
      }

      updateSitePage({
        spaceId: pageData.spaceId,
        blockId: pageData.pageId,
        attrs,
        tenantId: tenantId || '',
      });
    },
    [editor, tenantId]
  );

  const addFloadNav = useCallback(() => {
    // 存在导航则不再处理
    if (currentPageAttrs.floatNav?.navigatList?.length) {
      message.warning(t('build_onlineFile_addFail'));
      return;
    }
    const attrs = {
      ...currentPageAttrs,
      floatNav: {
        navigatList: [
          {
            id: generateUniqueId(),
            src: 'https://img.huahuabiz.com/user_files/2024119/1705655196017257.png',
            text: `${t('build_onlineFile_noImg')}`,
            link: null,
          },
        ],
        position: 'bottom',
        bgColor: '#fff',
        showTitle: true,
      },
    };
    updatePageAttrs(attrs);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPageAttrs, updatePageAttrs]);

  useEffect(() => {
    if (editor) {
      setEvent('floatNav', addFloadNav);
    }
  }, [editor, addFloadNav]);

  const PageContextProvider = useMemo(
    () => ({
      updatePageList: getPageList,
      toHome: () => {
        navigate(`/file/${pageList[0].blockId}123?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
          replace: true,
        });
      },
      currentPageAttrs,
      updatePageAttrs,
      watchUpdate,
      content,
      updateEditor: setEditor,
    }),
    [
      getPageList,
      navigate,
      pageList,
      webSiteId,
      tenantId,
      currentPageAttrs,
      updatePageAttrs,
      watchUpdate,
      content,
      setEditor,
    ]
  );

  return (
    // @ts-ignore
    <Page
      title={[
        {
          title: `${t('build_onlineFile_buildManage')}`,
          to: '/build/manage',
        },
        {
          title: `${t('build_onlineFile_webManage')}`,
          to: `/website/manage?webSiteId=${webSiteId}&tenantId=${tenantId}`,
        },
        { title: `${t('build_onlineFile_webDecoration')}` },
      ]}
    >
      <PageContext.Provider value={PageContextProvider}>
        <NiceModal.Provider>
          <Layout collapsed={collapsed}>
            <Sider className="workspace-sider" slot="sider" logo={logo} siteName={name} />
            {blockId && (
              <Header
                slot="header"
                editor={editor}
                pageAttrs={currentPageAttrs}
                onPageAttrsChange={(attrs) => {
                  setCurrentPageAttrs(attrs);
                }}
              />
            )}
            <div className={style.wrapper} id={editorProviderParentId} slot="main">
              <Suspense fallback={<Spin spinning />}>
                <Outlet />
              </Suspense>
            </div>
          </Layout>
        </NiceModal.Provider>
      </PageContext.Provider>
    </Page>
  );
}

export default OnlineFile;

.customModal {
  :global {
    .ant-modal-content {
      width: 480px;
      margin: 0;
      padding: 16px;

      .ant-modal-body {
        height: 240px;
        padding: 0;
        overflow-y: scroll;
      }

      .ant-modal-footer {
        border-top: 1px solid #f3f3f3;
        text-align: center;
        padding: 12px 0 0 0;
      }
    }
  }
}

.treeBox {
  display: flex;
  align-items: center;
}

.treeItemFlexBox {
  display: flex;
  align-items: center;
}

.treeItemIcon {
  position: absolute;
  left: -20px;
  z-index: 999;
  color: #000;
  font-size: 16px;

  :global {
    svg {
      color: #000;
    }
  }
}

@keyframes rotate-90 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

.treeItemIconRotate {
  animation: rotate-90 0.3s forwards;
}

.outsideRadio {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  border: 1px solid #b1b3be;
  border-radius: 50%;
}

.outside {
  border: 1px solid #441eff;
}

.withinRadio {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #441eff;

  &:hover {
    background-color: #441eff !important;
  }
}

import React, { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ModalProps, Tree, message } from '@echronos/antd';
import classNames from 'classnames';
import { cloneDeep, isFunction } from 'lodash';
import getSpaceList, { SpaceListType } from '@/apis/site-manager/get-space-list';
import Icon from '@echronos/echos-icon';
import getSpaceFirstPages from '@/apis/site-manager/get-space-first-pages';
import getPageChildPages from '@/apis/site-manager/get-page-child-page';
import styles from './custom-tree.module.less';

export interface CustomTreeProps extends ModalProps {
  onConfirm: MultipleParamsFn<[info: { parentId: string; parentType: string }]>;
  tenantId: string;
}

function CustomTree({ tenantId, onConfirm, ...props }: PropsWithChildren<CustomTreeProps>) {
  const { t } = useTranslation();
  const [spaceList, setSpaceList] = useState<SpaceListType[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectNodeInfo, setSelectNodeInfo] = useState<any>({});

  const onClose = () => {
    if (isFunction(props.onCancel)) {
      // @ts-ignore
      props.onCancel();
    }
  };

  const onOk = () => {
    if (selectNodeInfo.type) {
      const parentId =
        selectNodeInfo.type === 'space' ? selectNodeInfo.spaceId : selectNodeInfo.blockId;
      onConfirm({ parentId, parentType: selectNodeInfo.type });
      onClose();
    } else {
      message.warning(t('build_custom_selectTip'));
    }
  };

  const updateTreeData = useCallback(
    (list: any[], key: React.Key, children: any[]): any[] =>
      list.map((node) => {
        if (node.key === key) {
          return {
            ...node,
            children,
          };
        }
        if (node.children) {
          return {
            ...node,
            children: updateTreeData(node.children, key, children),
          };
        }
        return node;
      }),
    []
  );

  const onLoadData = useCallback(
    (treeNode: any, isNextChild = false, keys = [], isFirst = false) => {
      const { spaceId, blockId } = treeNode;
      const pageType = treeNode.type;
      const fn = pageType === 'space' ? getSpaceFirstPages : getPageChildPages;
      const params = pageType === 'space' ? { spaceId, tenantId } : { blockId, tenantId };
      const id = pageType === 'space' ? spaceId : blockId;
      fn(params as any).then((res) => {
        res.list.forEach((item) => {
          const items = item;
          items.name = items.attrs?.pageName || '';
          items.logo = items.attrs?.logo || '';
          items.key = item.blockId;
          items.type = 'page';
        });
        if (res.list && res.list.length) {
          const arr = updateTreeData(spaceList, id, [...res.list]);
          setSpaceList(arr);
          if (isNextChild) {
            let info;
            if (isFirst) {
              info = res.list[0];
            } else {
              info = res.list[res.list.length - 1];
            }
            setSelectedKeys([info.blockId]);
          }
          if (keys && keys.length) {
            setExpandedKeys(cloneDeep(keys));
          }
        }
      });
    },
    [spaceList, tenantId, updateTreeData]
  );

  const findChild = (arr: any, result: any) => {
    if (arr && arr.length) {
      arr.forEach((item: any) => {
        result.push(item.key);
        if (item.children && item.children.length) {
          findChild(item.children, result);
        }
      });
    }
  };

  // 展开收起
  const onSelectExpanded = useCallback(
    (val: SpaceListType, isRequest = false) => {
      if (val.type === 'space' || isRequest) {
        if (!expandedKeys.includes(val.key)) {
          expandedKeys.push(val.key);
          onLoadData(val, false, expandedKeys);
        } else {
          const arr = expandedKeys.filter((item) => item !== val.key);
          const result: any = [];
          findChild(cloneDeep(val.children), result);
          const arrVal = arr.filter((item) => !result.includes(item));
          setExpandedKeys(cloneDeep(arrVal));
        }
      }
    },
    [expandedKeys, onLoadData] // eslint-disable-line
  );

  const titleRender = useCallback(
    (nodeData) => {
      return (
        <div
          className={styles.treeBox}
          onClick={() => {
            onSelectExpanded(nodeData);
          }}
        >
          {(nodeData.hasChild || (nodeData.children && !!nodeData.children.length)) && (
            <Icon
              name="secondary_dropdown_arrow"
              size={18}
              color="#000"
              className={classNames(styles.treeItemIcon, {
                [styles.treeItemIconRotate]: expandedKeys.includes(nodeData.key),
              })}
              onClick={(event) => {
                event.stopPropagation();
                onSelectExpanded(nodeData, true);
              }}
            />
          )}
          <div
            className={styles.treeItemFlexBox}
            onClick={(e) => {
              e.stopPropagation();
              setSelectNodeInfo(nodeData);
            }}
          >
            <div
              className={classNames(
                styles.outsideRadio,
                nodeData.blockId === selectNodeInfo.blockId && styles.outside
              )}
            >
              <div
                className={classNames(
                  nodeData.blockId === selectNodeInfo.blockId && styles.withinRadio
                )}
              />
            </div>
            <div
              className={styles.treeItemName}
              title={nodeData.name || `${t('build_custom_noName')}`}
            >
              {nodeData.name || `${t('build_custom_noName')}`}
            </div>
          </div>
        </div>
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [expandedKeys, selectNodeInfo.blockId, onSelectExpanded]
  );

  useEffect(() => {
    getSpaceList({ tenantId }).then((res) => {
      const newList = res.list.map((item) => ({
        ...item,
        blockId: item.spaceId,
        key: item.spaceId,
        type: 'space',
      }));
      setSpaceList(newList);
    });
  }, [tenantId]);

  return (
    <Modal
      {...props}
      centered
      width={480}
      wrapClassName={styles.customModal}
      closable={false}
      onOk={onOk}
      onCancel={onClose}
    >
      <Tree
        treeData={spaceList}
        titleRender={titleRender}
        expandedKeys={expandedKeys}
        selectedKeys={selectedKeys}
        onExpand={(expandedKey, { expanded, node }) => {
          if (expanded) {
            onLoadData(node);
          }
          setExpandedKeys([...expandedKey] as string[]);
        }}
      />
    </Modal>
  );
}

export default CustomTree;

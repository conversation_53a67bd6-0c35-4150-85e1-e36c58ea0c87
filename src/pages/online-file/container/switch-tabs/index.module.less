.tabsBox {
  display: flex;
  justify-content: flex-start;

  .collapsed-trigger {
    cursor: pointer;
    line-height: 1;
    padding: 4px;
    transition: all 0.3s;
    width: 22px;
    height: 22px;
    border-radius: 4px;
    margin-right: 16px;

    &.active {
      background-color: var(--workspace-hover-color);
    }

    &.collapsed {
      position: static;
      transition: all 0.1s;
    }
  }

  .noTabs {
    display: flex;
    margin-right: 33px;
    padding-bottom: 12px;
    font-size: 18px;
    font-weight: 500;
    color: #040919;
    cursor: pointer;
  }

  .selectTabs {
    display: flex;
    cursor: pointer;
    border-bottom: 4px solid #441eff;
    color: #441eff;
  }
}

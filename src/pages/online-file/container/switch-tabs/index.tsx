import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { Tooltip } from '@echronos/antd';
import { MenuOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { useSiderStore } from '@/store/online-file/use-sider';
import styles from './index.module.less';

interface tabListType {
  id: number;
  name: string;
}

interface SwitchTabsProps {
  defaultActive: number;
  tabList: tabListType[];
  onTabsChange: MultipleParamsFn<[val: number]>;
}

function SwitchTabs({ defaultActive, tabList, onTabsChange }: SwitchTabsProps) {
  const { t } = useTranslation();
  const { collapsed, levitate, setSiderToggle, setSiderLevitateShow, setSiderLevitateHide } =
    useSiderStore((state) => state);

  return (
    <div className={styles.tabsBox}>
      <div className={styles['collaps-menu']}>
        {collapsed ? (
          <Tooltip title={t('build_tabs_expand')}>
            <div
              className={classNames(
                styles['collapsed-trigger'],
                !!collapsed && styles.collapsed,
                !!levitate && styles.active
              )}
              onClick={() => setSiderToggle(!collapsed)}
              onMouseEnter={() => setSiderLevitateShow()}
              onMouseLeave={() => setSiderLevitateHide()}
            >
              {levitate ? <DoubleRightOutlined /> : <MenuOutlined />}
            </div>
          </Tooltip>
        ) : null}
      </div>
      {tabList.map((item) => (
        <div
          key={item.id}
          className={classNames(styles.noTabs, defaultActive === item.id && styles.selectTabs)}
          role="button"
          tabIndex={0}
          onClick={() => {
            onTabsChange(item.id);
          }}
        >
          <div>{item.name}</div>
        </div>
      ))}
    </div>
  );
}

export default SwitchTabs;

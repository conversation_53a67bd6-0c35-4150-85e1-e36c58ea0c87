import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { message, Modal } from 'antd';
import { Price } from '@echronos/echos-ui';
import { PublishNotes } from '@echronos/editor/dist/component-publish';
import { TemplateInfoResultType } from '@/apis/site-manager/get-template-info';
import styles from './index.module.less';

interface PublishNotesModalType {
  isVisible: boolean;
  data: TemplateInfoResultType;
  onConfirm: (info: any) => void;
  onCancel: () => void;
}

function PublishNotesModal({ isVisible, data, onConfirm, onCancel }: PublishNotesModalType) {
  const { t } = useTranslation();
  const notesInfo = useRef<any>(null);

  return (
    <Modal
      title={null}
      visible={isVisible}
      closable={false}
      maskClosable
      centered
      footer={null}
      onCancel={onCancel}
      className={styles.publishModal}
    >
      <PublishNotes
        isReceptioDesk="0"
        ref={notesInfo}
        echoData={{
          title: data?.name,
          imageList: [data?.coverImage],
          topicList: data?.topicList,
        }}
        publishNotesEvent={() => {
          const editInfo = notesInfo.current.pageAll();
          const newEditInfo = {
            ...editInfo,
            productType: 1,
            productIds: [data.id],
          };
          if (!newEditInfo.imageList.length) return message.warning(t('build_notes_coverTip'));
          if (!newEditInfo.title) return message.warning(t('build_notes_titleTip'));
          onConfirm(newEditInfo);
        }}
        cancelEvent={onCancel}
      >
        <div className={styles.titleBox}>
          <div className={styles.line} />
          {t('build_notes_relatedGoods')}
        </div>
        <div className={styles.appFlexBox}>
          <div className={styles.appBox}>
            <div className={styles.iconBox}>
              {data?.appList?.slice(0, 4)?.map((item) => (
                <img key={item.appIcon} className={styles.appIcon} src={item.appIcon} alt="" />
              ))}
            </div>
          </div>
          <div>
            <div className={styles.productName}>{data?.name}</div>
            <div className={styles.productCount}>
              {t('build_notes_contain')}
              {data?.appList?.length}
              {t('build_notes_apps')}
            </div>
            <Price value={Number(data?.marketPrice)} className={styles.price} separate format />
          </div>
        </div>
      </PublishNotes>
    </Modal>
  );
}

export default PublishNotesModal;

.publishModal {
  :global {
    margin-right: 300px;
    .ant-modal-content {
      width: 860px;
      margin: 0;
      padding: 16px;

      .ant-modal-body {
        height: 500px;
        padding: 0;
        overflow-y: scroll;
      }

      .ant-modal-footer {
        border-top: 1px solid #f3f3f3;
        text-align: center;
        padding: 12px 0 0 0;
      }
    }
  }
}

.titleBox {
  display: flex;
  align-items: center;
  height: 26px;
  margin: 32px 0;
  font-size: 20px;
  font-weight: 500;
}

.line {
  height: 18px;
  width: 6px;
  border-radius: 67px;
  margin-right: 4px;
  background-color: #006eff;
}

.appFlexBox {
  display: flex;
  margin-bottom: 40px;
}

.appBox {
  width: 116px;
  height: 116px;
  margin-right: 20px;
  padding: 9px;
  box-sizing: border-box;
  border-radius: 25px;
  background: #f3f3f3;
  border: 1px solid rgb(0 0 0 / 4%);
}

.iconBox {
  display: flex;
  width: 100%;
  height: 100%;
  flex-flow: row wrap;
  // align-items: center;
  position: relative;
}

.appIcon {
  width: 47%;
  height: 46px;
  position: relative;
}

.appIcon:nth-child(2n-1) {
  margin-right: 4px;
  margin-bottom: 4px;
}

.price {
  font-size: 20px;
}

.productName {
  height: 36px;
  line-height: 36px;
  font-size: 24px;
  font-weight: 500;
}

.productCount {
  width: 77px;
  height: 20px;
  line-height: 20px;
  margin-bottom: 26px;
  text-align: center;
  font-size: 12px;
  border: 1px solid #ff7d00;
  border-radius: 2px;
  color: #ff7d00;
}

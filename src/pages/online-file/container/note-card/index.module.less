.noteCard {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2.01px 6.03px 0 rgb(21 72 191 / 20%);
  cursor: pointer;
}

.posterBox {
  padding-bottom: 60%;
  overflow: hidden;
  position: relative;

  :global {
    .ant-image {
      width: 100%;
      height: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .ant-image-img {
      object-fit: cover;
      height: 100%;
    }
  }
}

.appInfoBoxBox {
  padding: 12px;
}

.noteName {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.noteDesc {
  width: 100%;
  height: 60px;
  color: #888b98;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
}

.flexAppBox {
  width: 100%;
  display: flex;
  margin-top: 18px;
  align-items: center;
  overflow-x: auto;
}

.appIcon {
  flex-shrink: 0;
  width: 35px;
  height: 35px;
  margin-right: 5px;
  border-radius: 12px;
  background: #5c00fb;
}

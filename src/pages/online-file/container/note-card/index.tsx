import { Image } from '@echronos/antd';
import type { GetTemplateNoteListType } from '@/apis/cms';
import styles from './index.module.less';

interface NoteCardItemType {
  data: GetTemplateNoteListType;
  onOpenChange: (valId: number) => void;
}

function NoteCard({ data, onOpenChange }: NoteCardItemType) {
  return (
    <div
      className={styles.noteCard}
      role="button"
      tabIndex={-1}
      onClick={(e) => {
        e.stopPropagation();
        onOpenChange(data.id);
      }}
    >
      <div className={styles.posterBox}>
        <Image src={data.coverImageList[0] as string} preview={false} />
      </div>
      <div className={styles.appInfoBoxBox}>
        <div className={styles.noteName}>{data.title}</div>
        <div className={styles.noteDesc}>{data.content}</div>
        {/* <div className={styles.flexAppBox}>
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
          <div className={styles.appIcon} />
        </div> */}
      </div>
    </div>
  );
}

export default NoteCard;

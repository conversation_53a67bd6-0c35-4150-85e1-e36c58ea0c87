.templateBox {
  position: relative;

  &:hover {
    .maskBox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: #000;
      opacity: 0.7;
      border-radius: 12px;
    }
  }
}

.templateCard {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 6px 0 rgb(21 72 191 / 20%);
  cursor: pointer;
}

.posterBox {
  padding-bottom: 60%;
  overflow: hidden;
  position: relative;

  :global {
    .ant-image {
      width: 100%;
      height: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .ant-image-img {
      object-fit: cover;
      height: 100%;
    }
  }
}

.appInfoBox {
  padding: 12px;
}

.templateName {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.flexAppBox {
  width: 100%;
  display: flex;
  margin-top: 18px;
  align-items: center;
  overflow-x: hidden;
  margin-bottom: 10px;
}

.appIcon {
  flex-shrink: 0;
  width: 35px;
  height: 35px;
  margin-right: 5px;
  border-radius: 12px;
  background: #5c00fb;
  overflow: hidden;
}

.lineBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #888b98;
}

.statusBox {
  width: 52px;
  height: 20px;
  line-height: 20px;
  text-align: center;
}

.cyan {
  background-color: #e8ffea;
  color: #00b42a;
}

.grey {
  background-color: #f3f3f3;
  color: #040919;
}

.maskBox {
  display: none;
}

.flexIconBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.itemBox {
  width: 42px;
  text-align: center;
}

.iconBox {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
  border-radius: 50%;
  cursor: pointer;
}

.iconName {
  margin-top: 5px;
  color: #fff;
}

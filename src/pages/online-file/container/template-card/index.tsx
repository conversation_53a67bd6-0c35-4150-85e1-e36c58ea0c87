import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { Image } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { TemplateResultType } from '@/apis/site-manager/get-template-buy-list';
import styles from './index.module.less';

interface TemplateCardItemType {
  type: number; // 1:最近使用   2:已购买的  3:我创建的
  data: TemplateResultType;
  onPreview: (value: string) => void;
  onUseTemplate: (value: string) => void;
  onReleaseNote: (value: string) => void;
}

function TemplateCard({
  type,
  data,
  onPreview,
  onUseTemplate,
  onReleaseNote,
}: TemplateCardItemType) {
  const { t } = useTranslation();

  return (
    <div className={styles.templateBox}>
      <div
        className={styles.templateCard}
        role="button"
        tabIndex={-1}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className={styles.posterBox}>
          <Image src={data.coverImage} preview={false} />
        </div>
        <div className={styles.appInfoBox}>
          <div className={styles.templateName}>{data.name}</div>
          <div className={styles.flexAppBox}>
            {data.appList.map((every) => (
              <div className={styles.appIcon} key={every.id}>
                <img src={every.appIcon} alt="" />
              </div>
            ))}
          </div>
          {type === 3 && (
            <div className={styles.lineBox}>
              <div>
                {t('build_template_designer')}
                {data.designerName}
              </div>
              <div
                className={classNames(styles.statusBox, data.isShield ? styles.grey : styles.cyan)}
              >
                {data.isShield
                  ? `${t('build_template_blocked')}`
                  : `${t('build_template_published')}`}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* data.isShield(是否屏蔽：1是 0否) || type === 2(已购买) */}
      <div className={styles.maskBox}>
        <div className={styles.flexIconBox}>
          <div
            className={styles.itemBox}
            style={{ margin: data.isShield || type === 2 ? '0 8px' : '0' }}
          >
            <div className={styles.iconBox} onClick={() => onPreview(data.indexId)}>
              <Icon name="hide_view_eyes" color="#fff" size={16} />
            </div>
            <div className={styles.iconName}>{t('build_template_preview')}</div>
          </div>
          <div
            className={styles.itemBox}
            style={{ margin: data.isShield || type === 2 ? '0 8px' : '0 16px' }}
          >
            <div className={styles.iconBox} onClick={() => onUseTemplate(data.indexId)}>
              <Icon name="edit_line" color="#fff" size={16} />
            </div>
            <div className={styles.iconName}>{t('build_template_use')}</div>
          </div>
          {data.isShield || type === 2 ? null : (
            <div className={styles.itemBox}>
              <div className={styles.iconBox} onClick={() => onReleaseNote(data?.id)}>
                <Icon name="work_order_line" color="#fff" size={16} />
              </div>
              <div className={styles.iconName}>{t('build_template_publishNotes')}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TemplateCard;

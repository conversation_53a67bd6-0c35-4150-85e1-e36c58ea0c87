import Masonry from 'react-masonry-css';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRequest, useSize } from 'ahooks';
import classNames from 'classnames';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Divider, message, Tooltip } from '@echronos/antd';
import { Context } from '@echronos/echos-ui';
import { useModal } from '@ebay/nice-modal-react';
import { NotesCardReactElement } from '@echronos/millet-ui';
import { MenuOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { generateUniqueId } from '@/utils/tools';
import { formatSoruceUrl } from '@/utils/utils';
import { NotebookVideo } from '@echronos/editor/dist/component-notebook-detail';
import getCommunityClassifyList, {
  CommunityClassifyListType,
} from '@/apis/site-manager/get-community-classify-list';
import createSpaceFirstPage from '@/apis/site-manager/create-space-first-page';
// import createPageChildPage from '@/apis/site-manager/create-page-child-page';
import getSpaceList from '@/apis/site-manager/get-space-list';
import { getTemplateNoteList, GetTemplateNoteListType } from '@/apis/cms';
import postUseTemplate from '@/apis/site-manager/post-use-template';
import { useSiderStore } from '@/store/online-file/use-sider';
// import CustomTreeModal from '../container/custom-tree';
import styles from './index.module.less';

const pageSize = 10;

function TemplateMarket() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const webSiteId = searchParams.get('webSiteId') || '';
  const tenantId = searchParams.get('tenantId') || '';
  // const createType = searchParams.get('type') || ''; // 从点击创建页面或者子页面进来会携带type
  // const spaceId = searchParams.get('spaceId') || '';
  // const pageBlockId = searchParams.get('pageBlockId') || '';

  const [load, setLoad] = useState(false);
  const [classList, setClassList] = useState<CommunityClassifyListType[]>([]);
  const [noteList, setNoteList] = useState<GetTemplateNoteListType[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageTotal, setPageTotal] = useState(0);
  const [selectClassInfo, setSelectClassInfo] = useState({} as CommunityClassifyListType);
  const [breakpointColumnsObj, setBreakpointColumnsObj] = useState({
    default: 4,
  });
  // 每个笔记卡片的左右边距
  const [verticalGapPx, setVerticalGapPx] = useState<string>('16px');
  // 每个笔记卡片容器的底部边距
  const [bottomGapPx, setBottomGapPx] = useState<string>('24px');
  // 控制笔记卡片圆角的类名
  const [cardClass, setCardClass] = useState<string>('');

  const { collapsed, levitate, setSiderToggle, setSiderLevitateShow, setSiderLevitateHide } =
    useSiderStore((state) => state);

  const containerSize = useSize(document.querySelector('#editorProviderPartent'));

  const notebookVideoModal = useModal(NotebookVideo());

  const { run: queryNoteList, loading } = useRequest(getTemplateNoteList, {
    debounceWait: 500,
    manual: true,
    onSuccess: (res, params) => {
      setPageTotal(res.pagination.total);
      if (res.list) {
        res.list.forEach((item) => {
          if (item.coverImageList?.[0]) {
            // eslint-disable-next-line no-param-reassign
            item.coverImageList = [formatSoruceUrl(item.coverImageList?.[0])];
          }
        });
      }
      if (params[0].pageNo === 1) {
        setNoteList(res.list);
      } else {
        setNoteList((val) => val?.concat(res.list));
      }

      // console.log('[log res]: ', res);
      setPageNo(params[0].pageNo || 1);
    },
  });

  // 获取社区模板分类
  const queryClassList = () => {
    getCommunityClassifyList().then((res) => {
      // 在这里重置一下数据顺序，避免后端返回的顺序错误。将自定义页面卡片放在第一位
      const newList = [
        ...res.list.filter((item) => item.type === 2),
        ...res.list.filter((item) => item.type !== 2),
      ];
      setClassList(newList);
      // 除自定义卡片外还有其它分类，默认获取第一个分类下的笔记列表
      if (newList && newList.length > 1) {
        const defaultInfo = newList[1];
        setSelectClassInfo(defaultInfo);
        queryNoteList({ topicName: defaultInfo.name, pageNo, pageSize });
      }
    });
  };

  // 获取更多笔记列表
  const onMore = () => {
    if (pageNo >= pageTotal) return;
    queryNoteList({ topicName: selectClassInfo.name, pageNo: pageNo + 1, pageSize });
  };

  const onSwitchClass = (value: CommunityClassifyListType) => {
    setSelectClassInfo(value);
    queryNoteList({ topicName: value.name, pageNo: 1, pageSize });
  };

  // 打开笔记弹窗
  const openNoteModal = (valId: number) => {
    notebookVideoModal.show({
      scene: 'template',
      businessId: valId,
      businessType: 2,
      // @ts-expect-error todo
      templateViewOrigin: import.meta.env.BIZ_FRONT_SITE,
      getContainer: document.querySelector('.notebook-detail-wrap') as HTMLElement,
      onUseTemplate: (templateId?: string) => {
        setLoad(true);
        postUseTemplate({ id: templateId || '', tenantId })
          .then((res) => {
            message.success(t('build_enter_useTemplate'));
            navigate(
              `/file/${res.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}&isRefreshAll=2`,
              {
                replace: true,
              }
            );
          })
          .catch(() => {
            setLoad(false);
          });
        notebookVideoModal.remove();
        // CustomTreeModal({
        //   tenantId,
        //   onConfirm: (info) => {
        //     setLoad(true);
        //     postUseTemplate({ ...info, id: templateId || '' })
        //       .then((res) => {
        //         message.success('使用模板成功');
        //         navigate(`/file/${res.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
        //           replace: true,
        //         });
        //       })
        //       .finally(() => {
        //         notebookVideoModal.remove();
        //       });
        //   },
        // });
      },
    });
  };

  // 根据点击的创建页面以及创建子页面
  // const onSelectClass = (value: CommunityClassifyListType) => {
  //   if (value.type === 2) {
  //     const fn = createType === 'space' ? createSpaceFirstPage : createPageChildPage;
  //     const defaultParams = { spaceId, attrs: { pageName: '未命名的页面' } };
  //     const blockId = generateUniqueId();
  //     const firstBlockId = generateUniqueId();
  //     const firstBlock = {
  //       block: [
  //         {
  //           attrs: {
  //             blockId: firstBlockId,
  //             class: 'node-selectable',
  //             content: [],
  //             fullWidth: 'false',
  //             lineHeight: 24,
  //             padding: '0',
  //             margin: '0',
  //             parentId: '',
  //             rootBlockId: '',
  //             textAlign: 'left',
  //           },
  //           blockId: firstBlockId,
  //           content: [],
  //           parentId: blockId,
  //           type: 'paragraph',
  //         },
  //       ],
  //       content: [firstBlockId],
  //     };
  //     const params =
  //       createType === 'space'
  //         ? {
  //             blockId,
  //             tenantId,
  //             ...firstBlock,
  //             ...defaultParams,
  //           }
  //         : {
  //             tenantId,
  //             newBlockId: blockId,
  //             spaceId,
  //             attrs: { pageName: '未命名的页面' },
  //             blockId: pageBlockId,
  //             ...firstBlock,
  //           };
  //     fn({ ...(params as any) }).then(() => {
  //       message.success('创建成功');
  //       navigate(`/file/${blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}&updateSider=${1}`, {
  //         replace: true,
  //       });
  //     });
  //   } else {
  //     onSwitchClass(value);
  //   }
  // };

  // 直接创建空间下的一级页面
  const onCreatePage = () =>
    // value: CommunityClassifyListType
    {
      // if (value.type === 2) {
      getSpaceList({ tenantId }).then((res) => {
        if (res.list?.length) {
          const dataInfo = res.list[0];
          const defaultParams = {
            spaceId: dataInfo.spaceId,
            attrs: { pageName: `${t('build_custom_noName')}` },
          };
          const blockId = generateUniqueId();
          const firstBlockId = generateUniqueId();
          const firstBlock = {
            block: [
              {
                attrs: {
                  blockId: firstBlockId,
                  class: 'node-selectable',
                  content: [],
                  fullWidth: 'false',
                  lineHeight: 24,
                  padding: '0',
                  margin: '0',
                  parentId: '',
                  rootBlockId: '',
                  textAlign: 'left',
                },
                blockId: firstBlockId,
                content: [],
                parentId: dataInfo.spaceId,
                type: 'paragraph',
              },
            ],
            content: [firstBlockId],
          };
          const params = {
            blockId,
            tenantId,
            ...firstBlock,
            ...defaultParams,
          };

          createSpaceFirstPage({ ...(params as any) }).then(() => {
            message.success(t('build_sider_createSuccessMsg'));
            navigate(
              `/file/${blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}&updateSider=${1}&isRefreshAll=1`,
              {
                replace: true,
              }
            );
          });
        } else {
          message.warning(t('build_market_createTip'));
        }
      });
      // } else {
      //   onSwitchClass(value);
      // }
    };
  useEffect(() => {
    queryClassList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (containerSize) {
      if (containerSize.width >= 1480) {
        const addColumnNum = Math.floor((containerSize.width - 1480) / 300);
        setBreakpointColumnsObj({ default: 5 + addColumnNum });
        setVerticalGapPx('16px');
        setBottomGapPx('24px');
        setCardClass('');
      }
      if (containerSize.width < 1480 && containerSize.width >= 1228) {
        setBreakpointColumnsObj({ default: 5 });
        setVerticalGapPx('16px');
        setBottomGapPx('24px');
        setCardClass('');
      }
      if (containerSize.width < 1228 && containerSize.width >= 976) {
        setBreakpointColumnsObj({ default: 4 });
        setVerticalGapPx('12px');
        setBottomGapPx('24px');
        setCardClass('');
      }
      if (containerSize.width < 976 && containerSize.width >= 708) {
        setBreakpointColumnsObj({ default: 3 });
        setVerticalGapPx('12px');
        setBottomGapPx('24px');
        setCardClass('');
      }
      if (containerSize.width < 708) {
        setBreakpointColumnsObj({ default: 2 });
        setVerticalGapPx('3px');
        setBottomGapPx('5px');
        setCardClass('mil-note-card-cover-control');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [containerSize]);

  return (
    <Context container theme={null} className={styles.templateMarketPage} loading={loading || load}>
      <div className={styles.header}>
        {collapsed ? (
          <Tooltip title={t('build_tabs_expand')}>
            <div
              className={classNames(
                styles['collapsed-trigger'],
                !!collapsed && styles.collapsed,
                !!levitate && styles.active
              )}
              onClick={() => setSiderToggle(!collapsed)}
              onMouseEnter={() => setSiderLevitateShow()}
              onMouseLeave={() => setSiderLevitateHide()}
            >
              {levitate ? <DoubleRightOutlined /> : <MenuOutlined />}
            </div>
          </Tooltip>
        ) : null}
      </div>
      <div className={styles.flexNoteBox} id="flexNoteBox">
        <div className={styles.pageTitle}>{t('build_market_welcome')}</div>
        {/* 分类卡片 */}
        <div className={styles.flexClassBox}>
          {classList?.map((item) => (
            <div
              className={classNames(
                styles.classCardWrap,
                item.id === selectClassInfo?.id && item.type === 1 && styles.hoverStyles
              )}
              tabIndex={-1}
              role="button"
              onClick={() => {
                // 自定义创建网站页面卡片
                if (item.type === 2) {
                  onCreatePage();
                } else {
                  // 其他卡片
                  onSwitchClass(item);
                }
                // createType有值，是点击空间中的添加页面或子页面进来，无值是直接在创建在第一个空间下创建一级页面
                // if (createType) {
                //   onSelectClass(item);
                // } else {
                //   onCreatePage(item);
                // }
              }}
            >
              <div
                className={styles.classCard}
                style={{
                  background: `url(${item.backgroundImage})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat',
                }}
              >
                <div>
                  {item.style === 1 ? (
                    <img src={item.image} alt="" className={styles.classImg} />
                  ) : (
                    <span
                      className={classNames(
                        styles.label,
                        item.id === selectClassInfo?.id && styles.labelHover
                      )}
                    >
                      {item.label}
                    </span>
                  )}
                </div>
                <div className={styles.className}>{item.name}</div>
                <div className={styles.classDescribe}>{item.describe}</div>
              </div>
            </div>
          ))}
        </div>
        <Divider />
        {/* 笔记卡片 */}
        <InfiniteScroll
          dataLength={noteList.length}
          hasMore={pageTotal > pageNo}
          loader={
            loading ? <div style={{ textAlign: 'center' }}>{t('build_enter_loading')}</div> : null
          }
          endMessage={
            !loading && pageTotal === pageNo && noteList?.length ? (
              <Divider plain>
                <div>{t('build_enter_loaded')}</div>
              </Divider>
            ) : null
          }
          next={() => onMore()}
          scrollableTarget="flexNoteBox"
        >
          {/* <Row gutter={24}>
            {noteList.map((item) => (
              <Col key={item.id} xs={24} sm={12} md={12} lg={8} xl={8} xxl={6}>
                <NotesCardReactElement
                  note={item}
                  triggerEvent={(params) => {
                    openNoteModal(params.id);
                  }}
                />
              </Col>
            ))}
          </Row> */}
          <Masonry
            className={styles['my-masonry-grid']}
            breakpointCols={breakpointColumnsObj}
            columnClassName={styles['my-masonry-grid_column']}
            style={{ margin: `0 -${verticalGapPx}` }}
          >
            {noteList.map((item) => {
              return (
                <div
                  style={{
                    padding: `0 ${verticalGapPx} ${bottomGapPx} ${verticalGapPx}`,
                  }}
                >
                  <NotesCardReactElement
                    note={item}
                    cardClassName={cardClass}
                    triggerEvent={(params) => {
                      openNoteModal(params.id);
                    }}
                  />
                </div>
              );
            })}
          </Masonry>
        </InfiniteScroll>
      </div>
    </Context>
  );
}

export default TemplateMarket;

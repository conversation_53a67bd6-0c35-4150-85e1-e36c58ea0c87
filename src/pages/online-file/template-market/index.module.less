@import '@echronos/react/less/index';

.guidance-wrap-bg(@unit) {
  background: linear-gradient(@unit, #9462f9 0%, #ffa7f3 47%, #c0a7ff 97%);
}

.templateMarketPage {
  padding: 0 0 16px 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

.pageTitle {
  margin-bottom: 47px;
  font-size: 34px;
  font-weight: 500;
  text-align: center;
}

// 分类卡片- start
.flexClassBox {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 12px;

  // &::-webkit-scrollbar {
  //   height: 10px;
  // }
}

.hoverStyles {
  .guidance-wrap-bg(149deg);
  animation: 3s linear infinite bg-flow;
  box-shadow: 0 11px 23px 0 rgba(216, 142, 246, 50%);
}

.label {
  padding: 3px 8px;
  margin-bottom: 4px;
  text-align: center;
  font-size: 12px;
  border-radius: 70px;
  border: 1px solid #040919;
}

.labelHover {
  border: none;
  color: #fff;
  background: linear-gradient(100deg, #5e22ff 4%, #be1da8 104%);
}

.classCardWrap {
  margin-bottom: 2px;
  padding: 4px;
  border-radius: 12px;
  transition-duration: 0.3s;
  margin-right: 15px;

  &:hover {
    .hoverStyles();

    .label {
      border: none;
      color: #fff;
      background: linear-gradient(100deg, #5e22ff 4%, #be1da8 104%);
    }
  }
}

.classCard {
  width: 154px;
  height: 216px;
  border-radius: 12px;
  padding: 12px 9px 12px 12px;
  display: block;
  box-shadow: 0 2px 6px 0 rgba(21, 72, 191, 20%);
  cursor: pointer;
  outline: none;
  flex-shrink: 0;
  margin-right: 10px;
  white-space: pre-wrap;

  &:last-child {
    margin-right: 0;
  }
}

.classImg {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.className {
  margin: 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.classDescribe {
  font-size: 12px;
  color: #888b98;
}

.my-masonry-grid {
  display: flex;
  .my-masonry-grid_column {
  }
}

// 分类卡片 - end

// 笔记卡片 - start
.flexNoteBox {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
  margin-top: 24px;

  &::-webkit-scrollbar {
    width: 0;
  }

  :global {
    .infinite-scroll-component {
      overflow-x: hidden !important;
    }
  }
}

.header {
  .collapsed-trigger {
    cursor: pointer;
    line-height: 1;
    padding: 4px;
    transition: all 0.3s;
    width: 22px;
    height: 22px;
    border-radius: 4px;

    &.active {
      background-color: var(--workspace-hover-color);
    }

    &.collapsed {
      position: static;
      transition: all 0.1s;
    }
  }
}

@keyframes bg-flow {
  0% {
    .guidance-wrap-bg(149deg);
  }

  1% {
    .guidance-wrap-bg(152.6deg);
  }

  2% {
    .guidance-wrap-bg(156.2deg);
  }

  3% {
    .guidance-wrap-bg(159.8deg);
  }

  4% {
    .guidance-wrap-bg(163.4deg);
  }

  5% {
    .guidance-wrap-bg(167deg);
  }

  6% {
    .guidance-wrap-bg(170.6deg);
  }

  7% {
    .guidance-wrap-bg(174.2deg);
  }

  8% {
    .guidance-wrap-bg(177.8deg);
  }

  9% {
    .guidance-wrap-bg(181.4deg);
  }

  10% {
    .guidance-wrap-bg(185deg);
  }

  11% {
    .guidance-wrap-bg(188.6deg);
  }

  12% {
    .guidance-wrap-bg(192.2deg);
  }

  13% {
    .guidance-wrap-bg(195.8deg);
  }

  14% {
    .guidance-wrap-bg(199.4deg);
  }

  15% {
    .guidance-wrap-bg(203deg);
  }

  16% {
    .guidance-wrap-bg(206.6deg);
  }

  17% {
    .guidance-wrap-bg(210.2deg);
  }

  18% {
    .guidance-wrap-bg(213.8deg);
  }

  19% {
    .guidance-wrap-bg(217.4deg);
  }

  20% {
    .guidance-wrap-bg(221deg);
  }

  21% {
    .guidance-wrap-bg(224.6deg);
  }

  22% {
    .guidance-wrap-bg(228.2deg);
  }

  23% {
    .guidance-wrap-bg(231.8deg);
  }

  24% {
    .guidance-wrap-bg(235.4deg);
  }

  25% {
    .guidance-wrap-bg(239deg);
  }

  26% {
    .guidance-wrap-bg(242.6deg);
  }

  27% {
    .guidance-wrap-bg(246.2deg);
  }

  28% {
    .guidance-wrap-bg(249.8deg);
  }

  29% {
    .guidance-wrap-bg(253.4deg);
  }

  30% {
    .guidance-wrap-bg(257deg);
  }

  31% {
    .guidance-wrap-bg(260.6deg);
  }

  32% {
    .guidance-wrap-bg(264.2deg);
  }

  33% {
    .guidance-wrap-bg(267.8deg);
  }

  34% {
    .guidance-wrap-bg(271.4deg);
  }

  35% {
    .guidance-wrap-bg(275deg);
  }

  36% {
    .guidance-wrap-bg(278.6deg);
  }

  37% {
    .guidance-wrap-bg(282.2deg);
  }

  38% {
    .guidance-wrap-bg(285.8deg);
  }

  39% {
    .guidance-wrap-bg(289.4deg);
  }

  40% {
    .guidance-wrap-bg(293deg);
  }

  41% {
    .guidance-wrap-bg(296.6deg);
  }

  42% {
    .guidance-wrap-bg(300.2deg);
  }

  43% {
    .guidance-wrap-bg(303.8deg);
  }

  44% {
    .guidance-wrap-bg(307.4deg);
  }

  45% {
    .guidance-wrap-bg(311deg);
  }

  46% {
    .guidance-wrap-bg(314.6deg);
  }

  47% {
    .guidance-wrap-bg(318.2deg);
  }

  48% {
    .guidance-wrap-bg(321.8deg);
  }

  49% {
    .guidance-wrap-bg(325.4deg);
  }

  50% {
    .guidance-wrap-bg(329deg);
  }

  51% {
    .guidance-wrap-bg(332.6deg);
  }

  52% {
    .guidance-wrap-bg(336.2deg);
  }

  53% {
    .guidance-wrap-bg(339.8deg);
  }

  54% {
    .guidance-wrap-bg(343.4deg);
  }

  55% {
    .guidance-wrap-bg(347deg);
  }

  56% {
    .guidance-wrap-bg(350.6deg);
  }

  57% {
    .guidance-wrap-bg(354.2deg);
  }

  58% {
    .guidance-wrap-bg(357.8deg);
  }

  59% {
    .guidance-wrap-bg(1.4deg);
  }

  60% {
    .guidance-wrap-bg(5deg);
  }

  61% {
    .guidance-wrap-bg(8.6deg);
  }

  62% {
    .guidance-wrap-bg(12.2deg);
  }

  63% {
    .guidance-wrap-bg(15.8deg);
  }

  64% {
    .guidance-wrap-bg(19.4deg);
  }

  65% {
    .guidance-wrap-bg(23deg);
  }

  66% {
    .guidance-wrap-bg(26.6deg);
  }

  67% {
    .guidance-wrap-bg(30.2deg);
  }

  68% {
    .guidance-wrap-bg(33.8deg);
  }

  69% {
    .guidance-wrap-bg(37.4deg);
  }

  70% {
    .guidance-wrap-bg(41deg);
  }

  71% {
    .guidance-wrap-bg(44.6deg);
  }

  72% {
    .guidance-wrap-bg(48.2deg);
  }

  73% {
    .guidance-wrap-bg(51.8deg);
  }

  74% {
    .guidance-wrap-bg(55.4deg);
  }

  75% {
    .guidance-wrap-bg(59deg);
  }

  76% {
    .guidance-wrap-bg(62.6deg);
  }

  77% {
    .guidance-wrap-bg(66.2deg);
  }

  78% {
    .guidance-wrap-bg(69.8deg);
  }

  79% {
    .guidance-wrap-bg(73.4deg);
  }

  80% {
    .guidance-wrap-bg(77deg);
  }

  81% {
    .guidance-wrap-bg(80.6deg);
  }

  82% {
    .guidance-wrap-bg(84.2deg);
  }

  83% {
    .guidance-wrap-bg(87.8deg);
  }

  84% {
    .guidance-wrap-bg(91.4deg);
  }

  85% {
    .guidance-wrap-bg(95deg);
  }

  86% {
    .guidance-wrap-bg(98.6deg);
  }

  87% {
    .guidance-wrap-bg(102.2deg);
  }

  88% {
    .guidance-wrap-bg(105.8deg);
  }

  89% {
    .guidance-wrap-bg(109.4deg);
  }

  90% {
    .guidance-wrap-bg(113deg);
  }

  91% {
    .guidance-wrap-bg(116.6deg);
  }

  92% {
    .guidance-wrap-bg(120.2deg);
  }

  93% {
    .guidance-wrap-bg(123.8deg);
  }

  94% {
    .guidance-wrap-bg(127.4deg);
  }

  95% {
    .guidance-wrap-bg(131deg);
  }

  96% {
    .guidance-wrap-bg(134.6deg);
  }

  97% {
    .guidance-wrap-bg(138.2deg);
  }

  98% {
    .guidance-wrap-bg(141.8deg);
  }

  99% {
    .guidance-wrap-bg(145.4deg);
  }

  100% {
    .guidance-wrap-bg(149deg);
  }
}

import { Spin, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { Suspense } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { DoubleRightOutlined, MenuOutlined } from '@ant-design/icons';
import { useSiderStore } from '@/store/online-file/use-sider';
import styles from './index.module.less';

export default function Settings() {
  const { t } = useTranslation();
  const { pathname, search } = useLocation();

  const nav = useNavigate();

  const { collapsed, levitate, setSiderToggle, setSiderLevitateShow, setSiderLevitateHide } =
    useSiderStore((state) => state);

  const SIDER_MENUS = [
    // { title: '安全', path: '/file/settings/safe', position: 0 },
    // { title: '空间管理', path: '/file/settings/storage', position: 1 },
    { title: `${t('build_setting_webSeo')}`, path: '/file/settings/seo', position: 0 },
    { title: `${t('build_setting_language')}`, path: '/file/settings/language', position: 1 },
  ];

  const selectedItem = SIDER_MENUS.find((item) => item.path === pathname);

  console.log('selectedItem', selectedItem);

  const onSwitchPath = (path: string) => {
    nav(path + search);
  };

  return (
    <div className={styles.container}>
      <div className={styles.sider}>
        {collapsed ? (
          <Tooltip title={t('build_tabs_expand')}>
            <div
              className={classNames(
                styles['collapsed-trigger'],
                !!collapsed && styles.collapsed,
                !!levitate && styles.active
              )}
              onClick={() => setSiderToggle(!collapsed)}
              onMouseEnter={() => setSiderLevitateShow()}
              onMouseLeave={() => setSiderLevitateHide()}
            >
              {levitate ? <DoubleRightOutlined /> : <MenuOutlined />}
            </div>
          </Tooltip>
        ) : null}

        {SIDER_MENUS?.map((item) => (
          <div
            role="button"
            tabIndex={item.position}
            onClick={() => onSwitchPath(item.path)}
            className={classNames(
              styles['side-item'],
              selectedItem?.path === item.path && styles.selected
            )}
            key={item.path}
          >
            <div
              className={classNames(styles.line, selectedItem?.path === item.path && styles.back)}
            />
            {item.title}
          </div>
        ))}
      </div>
      <div className={styles.content}>
        <Suspense fallback={<Spin spinning />}>
          <Outlet />
        </Suspense>
      </div>
    </div>
  );
}

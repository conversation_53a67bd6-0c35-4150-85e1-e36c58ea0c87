.seo {
  font-size: 14px;
  max-width: 343px;
  line-height: 22px;
  padding-right: 70px;
  padding-left: 70px;
  box-sizing: content-box;
  letter-spacing: 0;

  :global .ant-input {
    padding: 5px 12px !important;
  }

  :global .ant-form-item-label {
    padding-bottom: 12px !important;

    label {
      font-size: 16px;
      line-height: 24px;
    }
  }

  :global .ant-form-item-control-input {
    min-height: 0;
  }

  :global .ant-form-item-extra {
    line-height: 22px;
    margin-top: 4px;
  }

  :global .ant-form-item:last-child {
    margin-bottom: 0;

    .ant-form-item-extra {
      margin-top: 0;
    }
  }

  :global .ant-input:hover {
    border-color: #823eff !important;
  }

  :global .ant-input:focus {
    border-color: #441eff !important;
  }

  .actions {
    display: flex;
    height: 36px;
    margin-top: 12px;
    margin-bottom: 24px;
    align-items: center;
    gap: 36px;
  }

  .link {
    color: #441eff;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.upload-smask {
  color: #fff;
  display: flex;
  justify-content: center;
  position: absolute;
  z-index: 9;
  transition: all 0.2s;
  inset: 0;
  opacity: 0;
  background: linear-gradient(0deg, rgb(0 0 0 / 50%), rgb(0 0 0 / 50%));
  align-items: center;
}

.upload {
  display: flex;
  width: 132px;
  height: 132px;
  overflow: hidden;
  justify-content: center;
  position: relative;
  transition: all 0.2s;
  border-radius: 10px;
  background: #f5f6fa;
  align-items: center;
  cursor: pointer;

  &:hover .upload-smask {
    opacity: 1;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
}

.icon {
  font-size: 20px;
}

.btn {
  display: flex;
  height: 32px;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(113deg, #441eff 0%, #823eff 100%);
  align-items: center;

  &:hover {
    background: linear-gradient(113deg, rgb(68 31 255 / 90%), rgb(129 61 255 / 90%)) !important;
  }

  span {
    line-height: 1;
  }
}

.tooltip {
  display: flex;
  align-items: center;
  gap: 9px;
}

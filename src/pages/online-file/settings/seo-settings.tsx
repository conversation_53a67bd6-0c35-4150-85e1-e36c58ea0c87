import { Button, Form, Image, Input, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { InfoCircleOutlined, PlusOutlined } from '@echronos/icons';
import { useMemoizedFn, useRequest } from 'ahooks';
import { Spin, Tooltip } from '@echronos/antd';
import getSeoBySite from '@/apis/base/get-seo-by-site';
import { useEffect } from 'react';
import updateBySite from '@/apis/base/update-seo-by-site';
import classNames from 'classnames';
import { SimpleUpload, SimpleUploadProps } from '@/components/upload';
import { useSearchParams } from 'react-router-dom';
import styles from './settings.module.less';

export default function SeoSettings() {
  const { t } = useTranslation();
  const TITLE_TIPS = `${t('build_setting_titleTip')}`;
  const DESCRIPTION_TIPS = `${t('build_header_descTips')}`;
  const KEYWORD_TIPS = `${t('build_header_keywordTips')}`;
  const [form] = Form.useForm();

  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';

  const info = useRequest(() => getSeoBySite({ tenantId }));
  const update = useRequest(updateBySite, { manual: true });

  const UploadRender = useMemoizedFn((p: { value?: string; onChange?: (v: string) => void }) => {
    const { value, onChange } = p;
    const handle: SimpleUploadProps['onChange'] = (v) => {
      onChange?.(v.file?.url || '');
    };

    return (
      <SimpleUpload
        className={classNames(styles.upload)}
        accept="image/*"
        maxCount={1}
        onChange={handle}
      >
        {value ? (
          <>
            <Image
              preview={false}
              width={120}
              height={120}
              className={styles['upload-img']}
              src={value}
              alt=""
            />
            <div className={styles['upload-smask']}>{t('build_setting_changeImg')}</div>
          </>
        ) : (
          <PlusOutlined className={styles.icon} />
        )}
      </SimpleUpload>
    );
  });

  const TooltipRender = useMemoizedFn((props: { tips: string; label: string }) => (
    <div className={styles.tooltip}>
      <div>{props.label}</div>
      <Tooltip title={props.tips}>
        <InfoCircleOutlined style={{ position: 'relative', top: -1 }} />
      </Tooltip>
    </div>
  ));

  const onSubmit = useMemoizedFn(async () => {
    const values = form.getFieldsValue();
    await update.runAsync({ ...values, id: info?.data?.id, tenantId });
    info.refresh();
    message.success(t('public_save_successMsg'));
  });

  useEffect(() => {
    if (!info.data) return;
    form.setFieldsValue(info.data);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [info.data]);

  return (
    <Spin spinning={info.loading}>
      <div className={styles.seo}>
        <Form form={form} layout="vertical">
          <Form.Item
            name="browserTitle"
            label={<TooltipRender tips={TITLE_TIPS} label={t('build_setting_browerTitle')} />}
          >
            <Input autoComplete="off" placeholder={t('build_setting_browerTitleInput')} />
          </Form.Item>
          <Form.Item
            name="browserIcon"
            extra={t('build_setting_imgTip')}
            label={t('build_setting_browerIcon')}
          >
            <UploadRender />
          </Form.Item>
          <Form.Item
            name="seoKeyword"
            extra={t('build_header_devideKeywords')}
            label={<TooltipRender tips={KEYWORD_TIPS} label={t('build_setting_seoKeyword')} />}
          >
            <Input.TextArea
              placeholder={t('build_header_keywordsInputTip')}
              autoSize={{ minRows: 6, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item
            name="seoDesc"
            label={<TooltipRender tips={DESCRIPTION_TIPS} label={t('build_setting_seoDesc')} />}
          >
            <Input.TextArea
              placeholder={t('build_header_seoInputTip')}
              autoSize={{ minRows: 6, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item extra={t('build_setting_attend')} label={t('build_setting_toBaidu')} />
        </Form>

        <div className={styles.actions}>
          <a
            href="https://ziyuan.baidu.com/linksubmit/url"
            target="__blank"
            className={styles.link}
          >
            {t('build_setting_toSubmit')}
          </a>
          <a
            href={`https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&rsv_idx=1&tn=baidu&wd=site%3A${info?.data?.domain}`} // TODO 需要后端传域名
            target="__blank"
            className={styles.link}
          >
            {t('build_setting_search')}
          </a>
        </div>

        <Button
          onClick={onSubmit}
          loading={update.loading}
          size="small"
          className={styles.btn}
          type="primary"
        >
          {t('public_save')}
        </Button>
      </div>
    </Spin>
  );
}

.container {
  display: flex;
  width: 100%;
  height: 100%;
}

.sider {
  display: flex;
  width: 142px;
  height: 100%;
  padding: 16px 0;
  overflow-y: auto;
  flex-shrink: 0;
  border-right: 1px solid #f3f3f3;
  // align-items: center;
  flex-direction: column;
  gap: 2px;

  .collapsed-trigger {
    cursor: pointer;
    line-height: 1;
    padding: 4px;
    transition: all 0.3s;
    width: 22px;
    height: 22px;
    border-radius: 4px;
    margin-left: 16px;

    &.active {
      background-color: var(--workspace-hover-color);
    }

    &.collapsed {
      position: static;
      transition: all 0.1s;
    }
  }
}

.side-item {
  font-size: 16px;
  width: 100%;
  line-height: 19px;
  padding: 14px 0 14px 0;
  position: relative;
  transition: all 0.2s;
  cursor: pointer;
  background-color: #fff;
  display: flex;
  align-items: center;

  &:hover,
  &.selected {
    background-color: #f5f6fa;
  }
}

.content {
  height: calc(100% - 30px);
  margin-top: 30px;
  padding-bottom: 30px;
  overflow-y: auto;
  flex: 1;
}

.line {
  width: 4px;
  height: 31px;
  margin-right: 26px;
  // position: absolute;
  // top: 0;
  // left: 0;
  // transition: all 0.2s;
  // transform: translateY(28px);
  border-radius: 0 2px 2px 0;
  opacity: 1;
}

.back {
  background: #823eff;
}

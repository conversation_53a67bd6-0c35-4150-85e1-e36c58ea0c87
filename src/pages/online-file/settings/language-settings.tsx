import { useTranslation } from 'react-i18next';
import { message, Select, Spin } from 'antd';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { updateSiteLanguage, getSiteLogo } from '@/apis';

import styles from './language.module.less';

export default function LanguageSettings() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const [loading, setLoading] = useState<boolean>(true);
  const [defaultVal, setDefaultVal] = useState<string>('');
  const [options] = useState([
    {
      value: '1',
      label: '简体中文',
    },
    {
      value: '2',
      label: 'English',
    },
  ]);

  const onChangeLang = (val: string) => {
    window.console.log('切换语言', val);
    updateSiteLanguage({
      tenantId,
      language: val,
    }).then(
      () => {
        setDefaultVal(val);
        message.success(t('build_setting_change_language_success'));
      },
      (error: any) => {
        message.success(t('build_setting_change_language_error'));
        window.console.log(error);
      }
    );
  };

  useEffect(() => {
    // 异步获取语言默认值
    const fetchDefaultValue = async () => {
      await getSiteLogo({ tenantId }).then((res) => {
        setDefaultVal(res.language);
        setLoading(false);
        window.console.log('获取站点语言', res.language);
      });
    };
    fetchDefaultValue();
  }, []); // eslint-disable-line

  return (
    <Spin spinning={loading}>
      <div className={styles.language}>
        <p className={styles.title}>{t('build_setting_language')}</p>
        <div className={styles.container}>
          <span>{t('build_setting_website_language')}</span>
          <Select
            value={defaultVal}
            style={{ width: 120 }}
            bordered={false}
            options={options}
            onSelect={onChangeLang}
          />
        </div>
        <span className={styles.explain}>{t('build_setting_language_explain')}</span>
      </div>
    </Spin>
  );
}

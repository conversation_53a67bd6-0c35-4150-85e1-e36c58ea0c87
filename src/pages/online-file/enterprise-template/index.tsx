import { useEffect, useState } from 'react';
import InfiniteS<PERSON>roll from 'react-infinite-scroll-component';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Col, Divider, Row, message } from '@echronos/antd';
import { Context, Empty } from '@echronos/echos-ui';
import { openURL } from '@echronos/core';
import getTemplateBuyList, { TemplateResultType } from '@/apis/site-manager/get-template-buy-list';
import getTemplateCreateList from '@/apis/site-manager/get-template-create-list';
import postUseTemplate from '@/apis/site-manager/post-use-template';
import getTemplateInfo, { TemplateInfoResultType } from '@/apis/site-manager/get-template-info';
import publishManageNotes from '@/apis/cms/publish-manage-notes';
import SwitchTabs from '../container/switch-tabs';
import TemplateCard from '../container/template-card';
// import CustomTreeModal from '../container/custom-tree';
import PublishNotesModal from '../container/publish-notes-modal';
import styles from './index.module.less';

const pageSize = 10;

function EnterpriseTemplate() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const webSiteId = searchParams.get('webSiteId') || '';
  const activeType = Number(searchParams.get('activeType')) || 2;
  const [load, setLoad] = useState(false);
  const [activeTab, setActiveTab] = useState(2);
  const [pageNo, setPageNo] = useState(1);
  const [pageTotal, setPageTotal] = useState(0);
  const [templateList, setTemplateList] = useState<TemplateResultType[]>([]);
  const [isShowPublish, setIsShowPublish] = useState(false);
  const [templateInfo, setTemplateInfo] = useState({} as TemplateInfoResultType);

  const requestApi = (type: number) => {
    switch (type) {
      case 2:
        return getTemplateBuyList;
      case 3:
        return getTemplateCreateList;
      default:
        return getTemplateBuyList;
    }
  };

  const { run, loading } = useRequest(requestApi(activeTab), {
    debounceWait: 500,
    manual: true,
    onSuccess: (res, params) => {
      setPageTotal(res.pagination.total);
      if (params[0].pageNo === 1) {
        setTemplateList(res.list);
      } else {
        setTemplateList((val) => val?.concat(res.list));
      }
      setPageNo(params[0].pageNo || 1);
    },
  });

  const onMore = () => {
    if (pageNo >= pageTotal) return;
    run({ pageNo: pageNo + 1, pageSize });
  };

  useEffect(() => {
    setActiveTab(activeType);
    run({ pageNo, pageSize });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Context
      container
      theme={null}
      className={styles.enterpriseTemplatePage}
      loading={loading || load}
    >
      <SwitchTabs
        tabList={[
          // { id: 1, name: '最近使用' },
          { id: 2, name: `${t('build_enter_purchased')}` },
          { id: 3, name: `${t('build_enter_create')}` },
        ]}
        defaultActive={activeTab}
        onTabsChange={(val) => {
          setActiveTab(val);
          setTemplateList([]);
          run({ pageNo: 1, pageSize });
        }}
      />

      <div className={styles.flexTemplateBox} id="flexTemplateBox">
        {templateList.length ? (
          <InfiniteScroll
            dataLength={templateList.length}
            hasMore={pageTotal > pageNo}
            loader={
              loading ? <div style={{ textAlign: 'center' }}>{t('build_enter_loading')}</div> : null
            }
            endMessage={
              !loading && pageTotal === pageNo && templateList?.length ? (
                <Divider plain>
                  <div>{t('build_enter_loaded')}</div>
                </Divider>
              ) : null
            }
            next={() => onMore()}
            scrollableTarget="flexTemplateBox"
          >
            <Row gutter={24}>
              {templateList.map((item) => (
                <Col key={item.id} xs={24} sm={24} md={12} lg={8} xl={8} xxl={6}>
                  <TemplateCard
                    key={item.id}
                    data={item}
                    type={activeTab}
                    onPreview={(blockId: string) => {
                      const link = `${
                        // @ts-expect-error todo
                        import.meta.env.BIZ_FRONT_SITE
                      }/render?rootBlockId=${blockId}&tenantId=${tenantId}`;
                      openURL(link);
                    }}
                    onUseTemplate={() => {
                      setLoad(true);
                      postUseTemplate({ id: item.id, tenantId })
                        .then((res) => {
                          message.success(t('build_enter_useTemplate'));
                          navigate(
                            `/file/${res.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}&isRefreshAll=2`,
                            {
                              replace: true,
                            }
                          );
                        })
                        .catch(() => {
                          setLoad(false);
                        });
                      // CustomTreeModal({
                      //   tenantId,
                      //   onConfirm: (info) => {
                      //     setLoad(true);
                      //     postUseTemplate({ ...info, id: item.id }).then((res) => {
                      //       message.success('使用模板成功');
                      //       navigate(
                      //         `/file/${res.blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`,
                      //         {
                      //           replace: true,
                      //         }
                      //       );
                      //     });
                      //   },
                      // });
                    }}
                    onReleaseNote={() => {
                      getTemplateInfo({ id: item.id }).then((success) => {
                        setTemplateInfo(success);
                        setIsShowPublish(true);
                      });
                    }}
                  />
                </Col>
              ))}
            </Row>
          </InfiniteScroll>
        ) : (
          <Empty style={{ marginTop: '10%' }} />
        )}
      </div>

      {isShowPublish && (
        <PublishNotesModal
          isVisible={isShowPublish}
          data={templateInfo}
          onConfirm={(val) => {
            publishManageNotes({ ...val }).then(() => {
              message.success(t('build_enter_publishSuccess'));
              setIsShowPublish(false);
            });
          }}
          onCancel={() => setIsShowPublish(false)}
        />
      )}
    </Context>
  );
}

export default EnterpriseTemplate;

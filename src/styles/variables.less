@prefixCls: 'block';

@link-color: #3d8dd8; // 链接色
@success-color: #52c41a; // 成功色
@warning-color: #faad14; // 警告色
@error-color: #EA1C26; // 错误色
@browser-default-font-size: 14;
@browser-default-font-size-px: 14px;


:root {
  --default-font-size: 14;
  --default-font-size-px: 14px;
  --default-font-size-md: 16px;
  --default-font-size-lg: 18px;
  --error-color: #EA1C26; // 错误色
  --workspace-primary-color: #441EFF; // 全局主色
  --workspace-hover-color: rgba(177, 179, 190, .2);
  --workspace-main-font-color: #040919;
  --workspace-base-font-color: rgba(4, 9, 25, 0.8);
  --workspace-minor-font-color: #888B98;
  --workspace-minor-border-color: rgba(177, 179, 190, .2);

  --pm-text-default-color: var(--workspace-main-font-color); //灰色
  --pm-text-gay-color: #888b98; //灰色
  --pm-text-brown-color: #64321e; //棕色
  --pm-text-orange-color: #f97c08; //橙色
  --pm-text-yellow-color: #f9ae08; //黄色
  --pm-text-green-color: #05d380; //绿色
  --pm-text-blue-color: #006eff; //蓝色
  --pm-text-purple-color: #823eff; //紫色
  --pm-text-pink-color: #ffb5b9; //粉色
  --pm-text-red-color: #ea1c26; //红色

  /*pm = proseMirror 编辑器文本背景色*/
  --pm-text-bg-default-color: transparent;
  --pm-text-bg-gay-color: #f5f6fa; //灰色
  --pm-text-bg-brown-color: rgba(100, 50, 30, 0.2); //棕色
  --pm-text-bg-orange-color: rgba(249, 124, 8, 0.2); //橙色
  --pm-text-bg-yellow-color: rgba(249, 174, 8, 0.2); //黄色
  --pm-text-bg-green-color: rgba(5, 211, 128, 0.2); //绿色
  --pm-text-bg-blue-color: rgba(0, 110, 255, 0.2); //蓝色
  --pm-text-bg-purple-color: rgba(130, 62, 255, 0.2); //紫色
  --pm-text-bg-pink-color: rgba(255, 181, 185, 0.2); //粉色
  --pm-text-bg-red-color: rgba(234, 28, 38, 0.2); //红色
}
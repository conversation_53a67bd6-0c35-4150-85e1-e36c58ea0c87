@import './high-hover';

// felx 布局
.flex-layout(@display: flex, @alignItems: normal, @justifyContent: normal,  @flexFlow: row nowrap, @flexDir: row) {
  display: @display;
  align-items: @alignItems;
  justify-content: @justifyContent;
  flex-flow: @flexFlow;
  flex-direction: @flexDir;
}

// 多行文本溢出
.ellipsis-multiple(@maxWidth: 100%, @line: 1, @display: -webkit-box) {
  display: @display;
  word-break: break-all;
  max-width: @maxWidth;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: @line;
  -webkit-box-orient: vertical;
}

// border 高亮
.border-highlight (@hover:var(--pm-text-purple-color), @focus: var(--workspace-primary-color) ) {
  &:hover {
    border-color: @hover;
  }

  &:focus {
    border-color: @focus;
  }
}

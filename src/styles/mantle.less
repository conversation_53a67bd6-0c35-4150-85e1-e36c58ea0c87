.ant-btn.gradual-primary {
  background: linear-gradient(113deg, #441eff 0%, #823eff 100%);
  border-radius: 10px;

  &:not(.ant-btn-lg, .ant-btn-sm) {
    min-height: 32px;
    padding: 4px 15px;
    transition: background 0.3s;
  }

  &:not(.ant-btn-loading, .ant-btn-text, .ant-btn-link) {
    &:not(.ant-btn-primary[disabled]):hover {
      background: linear-gradient(113deg, rgba(68, 31, 255, 0.9) 0%, rgba(129, 61, 255, 0.9) 100%);
    }
  }
}

.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: #fff;
  border-color: #b1b3be;
  background: #c6ccd8;
  text-shadow: none;
  box-shadow: none;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background: #441eff;
  border-color: #441eff;
}

.ant-radio {
  &-checked.ant-radio {
    .ant-radio-inner {
      border-color: var(--workspace-primary-color);

      &::after {
        background-color: var(--workspace-primary-color);
      }
    }
  }

  &-wrapper:hover .ant-radio {
    border-color: var(--workspace-primary-color);
  }
}

.ant-checkbox-checked:after {
  border: 1px solid #441eff;
}

.ant-modal-content {
  border-radius: 18px;
}

.ant-modal-confirm .ant-modal-body {
  padding: 20px;
}

// .ant-btn-primary {
//   background: linear-gradient(113deg, #441eff 0%, #823eff 100%);
// }

// 输入框
.ant-input-affix-wrapper {
  &:not(.ant-input-affix-wrapper-disabled) {
    // 非聚焦下的 hover
    &:not(.ant-input-affix-wrapper-focused) {
      &:hover {
        border-color: #823eff;
      }
    }
    // 聚焦
    &.ant-input-affix-wrapper-focused {
      border-color: #441eff;
    }
  }
}

// popover 阴影
.ant-popover-inner {
  box-shadow: -8px 8px 24px 0px rgba(2, 9, 58, 0.16);
}

.ant-tree-treenode.ant-tree-treenode-switcher-open :hover {
  background-color: #fff;
}

.ant-switch-checked {
  background: #441eff;
}

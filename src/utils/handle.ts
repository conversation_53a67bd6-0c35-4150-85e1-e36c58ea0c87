// 复制链接
import copy from 'copy-to-clipboard';
import { unsecuredCopyToClipboard } from '@/utils/tools';
import i18n from 'i18next';
import { message } from '@echronos/antd';
import { space } from '@/store';
import { cloneDeep } from 'lodash';
import { getPageData } from '@echronos/editor/dist/core';
import type { SpaceListType } from '@/apis/site-manager/get-space-list';
import getSpaceFirstPages from '@/apis/site-manager/get-space-first-pages';

export const onCopyLink = () => {
  const page = getPageData();
  // @ts-expect-error todo
  console.log('urlPath', import.meta.env.BIZ_FRONT_SITE);
  // @ts-expect-error todo
  const pageURL = `${import.meta.env.BIZ_FRONT_SITE}/doc/${page.pageId}`;
  try {
    if (window.isSecureContext && navigator.clipboard) {
      // navigator.clipboard.writeText(url);
      copy(pageURL);
    } else {
      unsecuredCopyToClipboard(pageURL);
    }
    message.success(`${i18n.t('public_copySuccessMsg')}`);
  } catch (error) {
    message.success(`${i18n.t('public_copyFailMsg')}`);
  }
};

// 查找树形结构的上一级
export const findParent = (data: any, target: string, result: any) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const item of data) {
    if (item.key === target) {
      result.unshift(item);
      return true;
    }
    if (item.children && item.children.length > 0) {
      // 根据查找到的结果往上找父级节点
      const isFind = findParent(item.children, target, result);
      if (isFind) {
        result.unshift(item);
        return true;
      }
    }
  }
  return false;
};

// 刪除
export const removeTree = (tree: any, idToRemove: string) => {
  // 遍历树的每个节点
  for (let i = 0; i < tree.length; i += 1) {
    const node = tree[i];
    // 检查当前节点的id是否是要删除的id
    if (node.key === idToRemove) {
      // 如果是，从数组中移除它
      tree.splice(i, 1);
      // 因为我们已经移除了一个元素，所以不需要继续检查后续的元素
      return tree;
    }
    // 如果当前节点有子节点，递归调用函数以处理子树
    if (node.children && node.children.length > 0) {
      removeTree(node.children, idToRemove);
    }
  }
};

// 更新
export const updateTree = (
  tree: any,
  type: string,
  idToRemove: string,
  key: string,
  value: string
) => {
  // 遍历树的每个节点
  for (let i = 0; i < tree.length; i += 1) {
    const node = tree[i];
    // 检查当前节点的id是否是要删除的id
    if (node[type] === idToRemove) {
      node[key] = value;
      // 因为我们已经移除了一个元素，所以不需要继续检查后续的元素
      return tree;
    }
    // 如果当前节点有子节点，递归调用函数以处理子树
    if (node.children && node.children.length > 0) {
      updateTree(node.children, type, idToRemove, key, value);
    }
  }
};

interface NavBlockPage {
  groupList: SpaceListType[];
  shareList: SpaceListType[];
  tenantId: string;
  navigate: any;
}

// 删除空间跳转页面
export const onNavPage = async ({ groupList, shareList, tenantId, navigate }: NavBlockPage) => {
  // 没有空间 没有共享
  if (!groupList || (!groupList.length && !shareList.length)) {
    navigate('/data/no-space');
    return;
  }
  // 如果空间里有页面
  const info = groupList.find((item) => item.hasChild);
  const res = await getSpaceFirstPages({ spaceId: info?.spaceId || '', tenantId });
  if (res.list && res.list.length) {
    const blockIdInfo = res.list[0];
    const headerArr = [
      { ...info },
      {
        ...blockIdInfo,
        logo: 'https://img.huahuabiz.com/user_files/2024625/1719301693061916.png',
        name: blockIdInfo.attrs.pageName,
      },
    ];
    space.setHeaderNavList(headerArr as any);
    navigate(`/${res.list[0].blockId}`);
    localStorage.setItem('HEADER_NAV_LIST', JSON.stringify(cloneDeep(headerArr)));
    return;
  }
  // 如果共享理由
  if (shareList && shareList.length) {
    navigate(`/${shareList[0].blockId}`);
  }
  navigate('no-page');
};

interface NavBlockProps {
  groupList: SpaceListType[];
  shareList: SpaceListType[];
  blockInfo: SpaceListType;
  navigate: any;
  type: string;
  webSiteId: string;
  tenantId: string;
}

// 删除页面跳转另一个一面
export const onNavBlock = ({
  groupList,
  shareList,
  blockInfo,
  navigate,
  type,
  webSiteId,
  tenantId,
}: NavBlockProps) => {
  if (groupList.length && type === 'group') {
    const result: any = [];
    findParent(groupList, cloneDeep(blockInfo.key), result);
    if (result && result.length) {
      result.pop();
      const arr = result.reverse();
      let info = arr.find((item: any) => item.type === 'page');
      if (info && info.key) {
        navigate(`/file/${info.key}?webSiteId=${webSiteId}&tenantId=${tenantId}`);
        return;
      }
      info = arr.find((item: any) => item.children && item.children.length);
      if (info && info.children.length > 1) {
        navigate(`/file/${info.children[0].blockId}?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
          replace: true,
        });
      } else {
        navigate(`/file/?isRefreshAll=1&webSiteId=${webSiteId}&tenantId=${tenantId}`, {
          replace: true,
        });
      }
    }
    return;
  }
  if (!shareList.length && !groupList.length) {
    navigate(`template-market?webSiteId=${webSiteId}&tenantId=${tenantId}`, {
      replace: true,
    });
  }
};

// 缓存头部
export const onUpdateHeaderNav = (val: any) => {
  const arr = space.headerNavList;
  if (arr && arr.length <= 1) {
    arr.push(val);
    space.setHeaderNavList(cloneDeep(arr));
  }
};

// 更新侧边栏
export const onUpdatePage = (data: any, blockId: string, type: string) => {
  const arr = type === 'group' ? cloneDeep(space.spaceGroupList) : cloneDeep(space.spaceShareList);
  updateTree(arr, 'blockId', blockId, 'name', data.title);
  updateTree(arr, 'blockId', blockId, 'logo', data.avatar.url);
  if (type === 'group') {
    space.setSpaceGroupList(cloneDeep(arr));
  } else {
    space.setSpaceShareList(cloneDeep(arr));
  }
};

// 更新头部标题logo
export const onUpdateHeader = async (data: any, blockId: string, type: string) => {
  let pageInfo = space.pageInfo;
  if (!cloneDeep(pageInfo.blockId) && localStorage.getItem('SITE_PAGE_INFO')) {
    pageInfo = JSON.parse(localStorage.getItem('SITE_PAGE_INFO') || '');
  }
  const headerArr = space.headerNavList;
  headerArr.forEach((item) => {
    const items = item;
    if (items.key === blockId) {
      items.logo = data.avatar.url;
      items.name = data.title;
    }
  });
  space.setHeaderNavList(cloneDeep(headerArr));
  onUpdatePage(data, blockId, type);
};

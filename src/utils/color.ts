// 获取透明度
export const getRgbaPercentages = (rgbaString: string) => {
  const match = rgbaString.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
  if (match) {
    return {
      red: parseInt(match[1], 10),
      green: parseInt(match[2], 10),
      blue: parseInt(match[3], 10),
      alpha: parseFloat(match[4]),
    };
  }
  return null;
};

// 更改透明度
export const changeRGBAOpacity = (rgba: string, val: number) => {
  const rgbaVal = rgba;
  // 确保透明度在0到1之间
  const newOpacity = Math.max(0, Math.min(1, val));
  // 分割RGBA字符串，获取RGB值和原始透明度
  const rgbaParts = rgbaVal.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d+(\.\d+)?)\)$/);
  if (rgbaParts) {
    // 提取RGB值和原始透明度
    const r = parseInt(rgbaParts[1], 10);
    const g = parseInt(rgbaParts[2], 10);
    const b = parseInt(rgbaParts[3], 10);
    parseFloat(rgbaParts[4]);
    // 创建新的RGBA字符串
    return `rgba(${r}, ${g}, ${b}, ${newOpacity})`;
  }
  return rgba;
};

// rgba转二进制
export const hexToRgb = (val: string, a: number) => {
  let hex = val;
  // 确保传入的hex字符串以#开头
  if (val.charAt(0) === '#') {
    hex = val.slice(1);
  }

  // 检查hex字符串的长度，如果是3位则转换为6位
  if (val.length === 3) {
    hex = val[0] + val[0] + val[1] + val[1] + val[2] + val[2];
  }
  // 转换RGB值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // 返回RGB对象或字符串
  // 或者如果你想返回一个字符串，可以这样做：
  return `rgba(${r}, ${g}, ${b}, ${a})`;
};

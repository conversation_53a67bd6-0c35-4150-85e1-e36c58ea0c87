/* eslint-disable no-param-reassign */
import { PageBlockDetail, IBlock } from '@/apis';
import { generateUniqueId } from '@/utils/tools';
import { JSONContent } from '@tiptap/core';
import i18n from 'i18next';
// import { message } from 'antd';

const textType = ['paragraph', 'heading'];

let maxError = 0;

export const transformBlock = (
  content: string[],
  blocksContent: JSONContent[],
  blockMap: Record<string, IBlock>,
  generateUuid?: boolean
) => {
  if (maxError >= 10) {
    console.error(`${i18n.t('build_utils_errorTimes')}`);
    return;
  }
  content.forEach((blockId) => {
    if (blockMap[blockId]) {
      const b: JSONContent = {
        type: blockMap[blockId].type,
        attrs: blockMap[blockId].attrs,
      };
      if (!!generateUuid && b.attrs) {
        b.attrs.blockId = generateUniqueId();
      }
      if (textType.includes(blockMap[blockId].type)) {
        b.content = blockMap[blockId].attrs.content;
      } else {
        b.content = [];
        transformBlock(blockMap[blockId].content, b.content, blockMap, generateUuid);
      }
      if (!generateUuid && b.attrs && b.attrs.blockId) {
        // 如果是容器模板可能会有问题
        b.attrs.blockId = blockId;
      }
      blocksContent.push(b);
    } else {
      maxError += 1;
      console.error(`${i18n.t('build_utils_nodeNoFound', { blockId })}`);
    }
  });
};

export const transformProtocols = (data: PageBlockDetail, json: JSONContent) => {
  maxError = 0;
  if (data.type === 'page') {
    json.type = 'doc';
    json.content = [];
    json.parentId = data.parentId;
    json.spaceId = data.spaceId;
    json.attrs = {
      blockId: data.blockId,
    };
    if (data.content && data.content.length > 0) {
      transformBlock(data.content, json.content, data.block);
    } else {
      json.content.push({
        type: 'paragraph',
        attrs: {
          fullWidth: 'false',
          blockId: generateUniqueId(),
        },
      });
    }
  }

  return json;
};

/**
 * 获取地址栏参数
 * @param name
 * @returns
 */
export function getQueryString(name: string, url?: string) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const query = (url || window.location.search).substring(1).match(reg);
  if (query) {
    return decodeURIComponent(query[2]);
  }
  return null;
}

// 代替window.open()，避免游览器默认拦截。
export const onWindowOpen = (href: string, target = '_blank') => {
  const a = document.createElement('a');
  a.href = href;
  a.target = target;
  a.click();
  a.remove();
};

// 生成唯一id值
export function createUuid() {
  let d = new Date().getTime();
  if (window.performance && typeof window.performance.now === 'function') {
    d += performance.now();
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    // eslint-disable-next-line no-bitwise
    const r = (d + Math.random() * 16) % 16 | 0; // d是随机种子
    d = Math.floor(d / 16);
    // eslint-disable-next-line no-bitwise
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}

/**
 * 链接后缀是否符合给定格式
 * @param src 图片地址
 * @param format 匹配格式
 * @returns 是否匹配
 */
export const matchSuffixFormat = (src: string, format?: string[]): boolean => {
  try {
    format = format || ['png', 'jpg', 'jpeg'];
    const pathname = new URL(src).pathname;
    const suffix = pathname.split('.').pop()?.toLowerCase() as string;
    return format.includes(suffix);
  } catch (error) {
    return false;
  }
};

/**
 * 格式化资源链接
 * @param src 链接
 * @param params 压缩参数
 * @returns src 处理完链接
 */
export const formatSoruceUrl = (src: string, params?: string): string => {
  // 匹配图片添加压缩属性
  if (matchSuffixFormat(src)) {
    params = params || 'x-image-process=image/format,webp/quality,Q_80';
    if (src.includes('?')) {
      return `${src}&${params}`;
    }

    return `${src}?${params}`;
  }

  return src;
};

{"name": "build-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "npx simple-git-hooks", "dev": "ech-micro --port 9030", "tsc": "tsc", "build": "ech-micro build && pnpm run version", "version": "node ./scripts/generate-version.js", "preview": "ech-micro preview --port 9030"}, "dependencies": {"@ant-design/icons": "^4.8.1", "@ebay/nice-modal-react": "^1.2.13", "@echronos/antd": "^4.24.14", "@echronos/core": "latest", "@echronos/echos-icon": "1.0.9", "@echronos/echos-ui": "2.0.4-test12-beta", "@echronos/editor": "1.3.62", "@echronos/icons": "latest", "@echronos/millet-ui": "1.4.72", "@echronos/react": "^1.0.1", "@swc/types": "0.1.5", "@tiptap/core": "^2.1.11", "@tiptap/extension-bubble-menu": "^2.1.12", "@tiptap/extension-code-block-lowlight": "^2.1.12", "@tiptap/extension-collaboration": "^2.1.11", "@tiptap/extension-color": "^2.1.12", "@tiptap/extension-focus": "^2.1.12", "@tiptap/extension-highlight": "^2.1.12", "@tiptap/extension-placeholder": "2.4.0", "@tiptap/extension-text-align": "^2.2.4", "@tiptap/extension-underline": "^2.1.12", "@tiptap/pm": "^2.1.11", "@tiptap/starter-kit": "^2.1.11", "ahooks": "~3.7.7", "antd": "^4.24.11", "classnames": "~2.3.2", "copy-to-clipboard": "~3.3.3", "core-js": "^3.31.0", "dayjs": "~1.11.8", "esdk-obs-browserjs": "^3.23.5", "i18next": "^24.2.0", "lodash": "~4.17.21", "lodash.debounce": "^4.0.8", "lowlight": "^2.7.0", "mitt": "^3.0.1", "mobx": "~6.9.0", "mobx-react-lite": "~3.4.3", "rc-upload": "^4.5.2", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-dom": "^17.0.2", "react-helmet": "~6.1.0", "react-i18next": "^15.2.0", "react-infinite-scroll-component": "~6.1.0", "react-masonry-css": "^1.0.16", "react-router-dom": "~6.3.0", "uuid": "^9.0.1", "y-indexeddb": "^9.0.12", "zustand": "^4.4.3"}, "devDependencies": {"@echronos/ech-micro": "^0.1.0", "@echronos/eslint-config": "0.0.2", "@echronos/swc-plugin-transform-imports": "^1.6.0", "@swc/core": "1.3.105", "@swc/jest": "^0.2.26", "@types/lodash": "^4.14.195", "@types/lodash.debounce": "^4.0.8", "@types/node": "^20.3.1", "@types/react": "^17.0.62", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^17.0.20", "@types/react-helmet": "^6.1.6", "@types/uuid": "^9.0.5", "babel-plugin-import": "^1.13.8", "cross-env": "^7.0.3", "eslint": "~8.22.0", "eslint-import-resolver-alias": "^1.1.2", "jest": "^29.5.0", "less": "^4.1.3", "lint-staged": "^12.3.7", "prettier": "^2.8.8", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-sass": "^1.12.21", "simple-git-hooks": "^2.8.1", "stylelint": "^15.10.1", "typescript": "^5.1.3"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --concurrent false"}, "lint-staged": {"**/*.{ts,tsx,less,css,json,js,jsx}": ["prettier --write --cache --ignore-unknown"], "**/*.{js,jsx,ts,tsx}": ["eslint --cache --fix"]}}
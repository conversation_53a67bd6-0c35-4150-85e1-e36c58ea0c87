{"root": true, "extends": ["@echronos/eslint-config"], "ignorePatterns": ["dist", "dist-ssr", "*.min.js", "micro.config.js", "micro.config.ts"], "rules": {"consistent-return": 0, "import/no-cycle": "off", "jsx-a11y/no-static-element-interactions": "off", "arrow-body-style": "off", "linebreak-style": ["error", "unix"], "no-shadow": "off", "@typescript-eslint/no-unused-vars": ["error"], "import/no-extraneous-dependencies": "off", "prefer-template": "off", "jsx-a11y/control-has-associated-label": "off", "no-console": "warn", "prefer-destructuring": "off", "react/function-component-definition": "off"}, "settings": {"import/resolver": {"alias": {"map": [["@", "./src"]], "extensions": [".ts", ".tsx", ".json", ".js"]}}}}
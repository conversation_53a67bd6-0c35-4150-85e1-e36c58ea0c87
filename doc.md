# 子应用

## 运行环境

- NodeJS >= 14.18.1
- pnpm >= 7.27.0

## 项目说明

- 大部分编辑器，都是基于 **Yjs** (Yjs-Shared Types) 这个技术来实现协同编辑功能，因为 **Yjs** 是一个基于 CRDT（Conflict-Free Replicated Data Type，无冲突复制数据类型）的算法实现的分布式系统框架。
- 在综合多个开源（Slatejs、`ProseMirrior` 、Lexical ）富文本编辑后，最终选定 ProseMirrior，主要有以下 3 点原因：
  - 有大公司背书，Google 也是用作者的另一个 CodeMirror 项目
  - 文档完善，生态强大
  - 不跟其他视图框架依赖，可以用 React、Vue、Angular 开发视图框架，可扩展性强
- 但完全基于 `ProseMirrior` 开发，所有扩展、组件都需要自行编写，效率太低，不符合我们实际需求；
- 所以在了解无头编辑器的过程，发现了它生态下比较强大且易于扩展的 `tiptap` 无头编辑器，它对部分常用的文本格式化（加粗、h1-h6、删除线、链接等）、表格、无/有序列表等进行封装，同时也支持底层的 ProseMirrior 的 插件机制，方便我们扩展开发业务需要的功能、让开发更高效。
- 相关文档地址：

  - ProseMirrior：https://prosemirror.net/docs
  - tiptap： https://tiptap.dev/

- 注意事项：
  - 在开发过程中，我们需要注意，ProseMirrior 它的 增、删、改，不不同于我们日常的开发习惯，我们习惯于通过某个节点、id、class 等去操控 dom，在它的设计里没有节点的概念，所有的行为操作都以 `pos`的理念呈现；
  - 示例（如富文本内有一段内容）：
  ```js
  第一行段落 1
  第二行段落 2,加粗的内容
  第三行段落 3倾斜的内容
  ```
  - 以上示例，"第一行段落 1" 它的起始`pos` 是 0,结束`pos`是`7`,"第二行段落 2,加粗的内容",它的起始`pos` 是 9，换行也算一个占位点，所以通常我们在基于某个节点的获取另一个节点的时候，会需要+1 进行处理；

## ProseMirrior 核心模块：

- `prosemirror-model` 定义了编辑器的文档模型
- `prosemirror-state` 提供了整个编辑器状态的数据结构
- `prosemirror-view` 实现一个用户界面组件，用来在浏览器中把编辑器的状态展示成可编辑元素
- `prosemirror-transform` 包含了一种可以记录/重放文档修改历史的功能组件

## src 目录结构

```
src
├── app.tsx
├── assets
│   ├── file-icon
│   │   ├── home-icon.png
│   └── logo.png
├── components
│   ├── layout
│       ├── index.tsx
│       └── layout.less
├── index.less
├── main.tsx
├── packages                                  参考tiptap的项目架构，该目录主要存放与富文本编辑器相关的插件、组件、或其他扩展
│   ├── components                            与 infras、widgets交互相关的公共组件，如image-align是bubble-image-bar扩展里，图片对齐方式的一个组件；
│   │   ├── bubble-ui                         自定的一个气泡悬浮组件，非特殊业务需要，建议还是不要使用，因为在跟 编辑器交互时没有办法充分结合，优先使用 tiptap的 BubbleMenu组件进行扩展
│   │   ├── cell                              根据UI风格封装的一个“行”高频使用组件，icon + 内容 + 指示icon
│   │   ├── image-align
│   │   ├── index.ts
│   │   ├── mask                              遮罩层，参考notion以及我们实际业务场景，多数用于在某些节点（如popover、bubble组合）操作时使用，如多层弹窗依次关闭
│   │   ├── popover                           基于antd的popover，结合我们的实际业务场景二次封装扩展使用;
│   │   └── types.ts
│   ├── core                                  编辑器操作、交互的一些相关公共方法
│   │   ├── constant.ts
│   │   ├── deleteNode.ts
│   │   ├── drag.ts
│   │   ├── getImagePos.ts
│   │   ├── index.ts
│   │   ├── node.ts
│   │   ├── setSelection.ts
│   │   ├── types.d.ts
│   │   └── unique-uid.ts
│   ├── index.ts
│   ├── infras                                主要存放编辑器需要的扩展block组件，如`/`下拉提示，分割线、image、Link等,每个扩展下应该有视图(block)、模型(model) 文件的原则划分管理
│   │   ├── bubble-slash-menu-extension       `/`下拉菜单
│   │   ├── code-block-lowlight-extension     代码块的扩展，引入`highlight.js` 支持代码高亮
│   │   ├── column-extension                  待完善
│   │   ├── divider-extension                 分割线
│   │   ├── draggable-extension               全局公共拖拉移动信息块的插件，还需完善；
│   │   ├── image-extension                   结合UI交互，自定义图片不同业务逻辑的呈现方式
│   │   ├── index.ts
│   │   ├── link-extension                    结合UI交互，实现a链接的新增、编辑、修改等
│   │   └── unique-id-extension               全局在编辑器交互过程中，自动增给每个节点新增一个节点id，在每个信息块的attrs属性里能获取到，id生成规则遵循 uuid规范
│   └── widgets                               小部件，即编辑器交互过程中常用的悬浮菜单栏（包括但不限于），多数为全局的方式，具体与某个节点如何交互需要自行实现；
│       ├── bubble-format-quick-bar           最常用的文本格式化： `加粗`、`下划线`、`删除线`、`链接`、`文字颜色`、`背景色`等操作菜单栏
│       ├── bubble-image-bar                  针对图片操作（对齐、展示方式、链接）等操作悬浮菜单
│       ├── bubble-image-resize               针对图片尺寸大小调整、并更新编辑器内对应节点的数据
│       ├── bubble-link-bar                   针对链接编辑、修改的气泡菜单
│       └── index.ts
├── pages
│   ├── home.tsx
│   └── workspaces                            站点工作台，
│       ├── components                        针对编辑器实例化、业务关联的组件，如config是针对当前业务需要的所有信息块、扩展组件组装点
│       ├── detail-editor                     实例化编辑器、并获取数据处理相关逻辑等
│       ├── layout                            针对工作台的布局组装包裹层、侧边栏等
│       └── routes.tsx
├── routes.ts
├── store
│   ├── packages                              对应的`src/packages`相关扩展需要的共享数据存储
│   │   ├── index.ts
│   │   └── use-draggble-bar.ts               存放`src/packages/infras/draggable-extension` 拖拽过程中部分位置节点交互数据，如：`pos`,便于在其他悬浮菜单内快速删除、编辑操作相应的节点；
│   ├── widgets                               对应的`src/packages/widgets` 相关插件需要的数据存储
│   │   ├── index.ts
│   │   ├── use-link-bar.ts
│   │   ├── use-menu-bar.ts
│   │   └── use-shared-bubble-bar.ts
│   └── workspace                             对应的`src/pages/workspaces` 业务逻辑需要的数据存储
│       ├── index.ts
│       ├── use-editor-instance.ts            对编辑器实例化的对象进行存储，可在使用 `useCurrentEditor` 无法获取编辑器实例的的场景下使用，如 “发布” 它独立于编辑器之外，是无法使用`useCurrentEditor` 获取到具体实例的
│       ├── use-page.ts                       对站点页面操作交互的数据存储
│       ├── use-sider.ts                      侧边栏交互的数据存储
│       └── useBubbleLinkInstance.ts
├── styles
│   ├── app.css
│   ├── mixins
│   └── variables.less
└── utils
    ├── index.ts
    ├── point.ts
    ├── rect.ts
    ├── shared.ts
    └── tools.ts
```

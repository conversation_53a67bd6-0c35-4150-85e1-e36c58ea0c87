/* eslint-disable no-unused-vars */
/// <reference types="@echronos/ech-micro/client" />
/// <reference types="@echronos/eslint-config/client.d.ts" />

interface ImportMetaEnv {
  readonly BIZ_API_URL: string;
  readonly BIZ_OBS_FOLDER: string;
  readonly BIZ_FRONT_SITE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare module '*.less' {
  const classes: { [key: string]: string };
  export default classes;
}

export declare type Timeout = ReturnType<typeof setTimeout>;

declare module 'esdk-obs-browserjs' {
  interface ObsClientOption {
    server: string; // 连接OBS的服务地址,可包含协议类型、域名、端口号
    access_key_id?: string; // 访问密钥中的AK
    secret_access_key?: string; // 访问密钥中的SK
    security_token?: string;
    timeout?: number; // 超时时间（单位：秒）
    is_cname?: number; // 是否通过自定义域名访问OBS服务
    useRawXhr?: boolean; // 是否使用原生XHR发送Ajax请求
  }

  interface ObsClientPermissions {
    // =================================
    // 权限类型
    // =================================
    PermissionRead: string; // 桶的读权限
    PermissionWrite: string; // 桶的写权限
    PermissionReadAcp: string; // 读ACP的权限，可以获取对应的桶或对象的权限控制列表（ACL）
    PermissionWriteAcp: string; // 写ACP的权限，则可以更新对应桶或对象的权限控制列表（ACL）
    PermissionFullControl: string; // 桶的完全控制权限

    // =================================
    // 预定义访问策略
    // =================================
    AclPrivate: string; // 私有读写
    AclPublicRead: string; // 公共读
    AclPublicReadWrite: string; // 公共读写
    AclPublicReadDelivered: string; // 桶公共读，桶内对象公共读
    AclPublicReadWriteDelivered: string; // 桶公共读写，桶内对象公共读写

    // =================================
    // 可被授权的类型
    // =================================
    GranteeGroup: string; // 授权给用户组
    GranteeUser: string; // 授权给单个用户

    // =================================
    // 可被授权用户组
    // =================================
    GroupAllUsers: string; // 所有用户

    // =================================
    // 存储类型
    // =================================
    StorageClassStandard: string; // 标准存储
    StorageClassWarm: string; // 低频访问存储
    StorageClassCold: string; // 归档存储

    // =================================
    // 取回选项
    // =================================
    RestoreTierExpedited: string; // 快速取回，取回耗时1~5分钟
    RestoreTierStandard: string; // 标准取回，取回耗时3~5小时

    // =================================
    // 事件类型
    // =================================
    EventObjectCreatedAll: string; // 所有创建对象事件
    EventObjectCreatedPut: string; // PUT上传对象事件
    EventObjectCreatedPost: string; // POST上传对象事件
    EventObjectCreatedCopy: string; // 复制对象事件
    EventObjectCreatedCompleteMultipartUpload: string; // 合并段事件
    EventObjectRemovedAll: string; // 所有删除对象事件
    EventObjectRemovedDelete: string; // 指定对象版本号删除对象事件
    EventObjectRemovedDeleteMarkerCreated: string; // 多版本开启后，不指定对象版本号删除对象事件

    // =================================
    // 元数据复制策略
    // =================================
    CopyMetadata: string; // 复制元数据
    ReplaceMetadata: string; // 替换元数据
  }

  export interface ObsClientCommonMsg {
    Status: number; // HTTP状态码，小于300表明操作成功；反之，表明操作失败。
    Code: string; // OBS服务端错误码，当Status小于300时为空。
    Message: string; // OBS服务端错误描述，当Status小于300时为空。
    HostId: string; // 请求的服务端ID，当Status小于300时为空。
    RequestId: string; // OBS服务端返回的请求ID。
    Id2: string; // OBS服务端返回的请求ID2。
    Indicator: string; // OBS服务端返回的详细错误码，当Status小于300时为空。
  }

  export interface ObsClientCommonInterfaceResult {
    RequestId: string; // OBS服务端返回的请求ID。
    Id2: string; // OBS服务端返回的请求ID2。
  }

  export type ObsClientProgressCallback = (
    uploadSize: number,
    totalSize: number,
    duration: number
  ) => void;

  export interface ObsClientPutObjectParameter {
    Bucket: string; // 桶名。
    Key: string; // 对象名。
    RequestDate?: string | Date; // 当为string类型时，必须符合ISO8601或RFC822规范。
    ACL?: string; // 创建对象时可指定的预定义访问策略。
    StorageClass?: string; // 创建对象时可指定的对象的存储类型。
    Body?: string; // 待上传对象的内容。
    SourceFile?: File | Blob; // 待上传的文件（浏览器必须支持FileReader）。
    ProgressCallback?: ObsClientProgressCallback; // 该回调函数依次包含三个参数：已上传的字节数、总字节数、已使用的时间（单位：秒）。
    Offset?: number; // 当设置了SourceFile时有效，代表源文件中某一分段的起始偏移大小，默认值为0， 单位为字节。
    Metadata?: Record<string, unknown>; // 待上传对象的自定义元数据。
    WebsiteRedirectLocation?: string; // 当桶设置了Website配置，该参数指明对象的重定向地址。
    Expires?: number; // 待上传对象的生命周期，单位：天。
    SuccessActionRedirect?: string; // 上传对象成功后的重定向的地址。
    ContentType?: string; // 待上传对象的MIME类型。
    ContentLength?: number; // 当设置了SourceFile时有效，代表待上传对象数据的长度。
    ContentMD5?: string; // 待上传对象数据的MD5值（经过Base64编码），提供给OBS服务端，校验数据完整性。
    SseKms?: 'kms'; // 以SSE-KMS方式加密对象，支持的值：
    SseKmsKey?: string; // SSE-KMS方式下加密的主密钥，可为空。
    SseC?: 'AES256'; // 以SSE-C方式加密对象，支持的值：
    SseCKey?: string; // SSE-C方式下加密的密钥，由AES256算法得到。
  }

  class ObsClient {
    constructor(option: ObsClientOption);

    // 预定义常量
    static readonly enums: ObsClientPermissions;

    // 初始化日志
    static initLog(parameter: { level: 'debug' | 'info' | 'warn' | 'error' }): void;

    // 上传对象
    putObject(
      parameter: ObsClientPutObjectParameter,
      callback?: (
        error: Error,
        result: {
          CommonMsg: ObsClientCommonMsg;
          InterfaceResult: ObsClientCommonInterfaceResult & {
            ETag: string;
            VersionId: string;
            StorageClass: string;
          };
        }
      ) => void
    ): void;

    // 追加上传
    appendObject(parameter: Record<string, unknown>): void;

    getObject(
      parameter: Record<string, unknown>,
      callback?: (
        error: Error,
        result: {
          CommonMsg: ObsClientCommonMsg;
          InterfaceResult: ObsClientCommonInterfaceResult & {
            ContentLength: string;
            Content: string | ArrayBuffer | Blob | { [key: string]: unknown };
          };
        }
      ) => void
    ): void;

    copyObject(parameter: Record<string, unknown>): void;

    deleteObject(
      parameter: Record<string, unknown>,
      callback?: (
        error: Error,
        result: {
          CommonMsg: ObsClientCommonMsg;
          InterfaceResult: ObsClientCommonInterfaceResult & {
            RequestId: string;
            DeleteMarker: string;
            VersionId: string;
          };
        }
      ) => void
    ): void;

    initiateMultipartUpload(parameter: Record<string, unknown>): void;

    uploadPart(parameter: Record<string, unknown>): void;

    copyPart(parameter: Record<string, unknown>): void;

    listParts(parameter: Record<string, unknown>): void;

    completeMultipartUpload(parameter: Record<string, unknown>): void;

    abortMultipartUpload(parameter: Record<string, unknown>): void;
  }

  export default ObsClient;
}

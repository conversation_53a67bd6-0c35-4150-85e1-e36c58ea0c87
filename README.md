# 子应用

## 运行环境

* NodeJS >= 14.18.1
* pnpm >= 7.27.0

## 项目结构

```
/                                                     // 根目录
├─ public                                             // 静态文件目录
│  └─ ...                                             // 静态文件
├─ src                                                // 项目目录
│  ├─ api                                             // 接口项目
│  │  └─ ...                                          // 接口文件
│  ├─ assets                                          // 项目资源目录
│  │  └─ ...                                          // 项目资源
│  ├─ conponents                                      // 项目自定义组件目录
│  │  └─ ...                                          // 项目自定义组件
│  ├─ containers                                      // 项目自定义容器目录
│  │  └─ ...                                          // 项目自定义容器
│  ├─ pages                                           // 项目自定义页面目录
│  │  └─ ...                                          // 项目自定义页面
│  ├─ app.tsx                                         // 项目应用启动组件
│  ├─ index.less                                      // 项目全局样式
│  ├─ main.tsx                                        // 项目启动文件
│  └─ routes.ts                                       // 自定义路由配置文件
├─ .editorconfig                                      // 文件格式配置
├─ .env                                               // 环境变量文件
├─ .eslintignore                                      // eslint 忽略配置文件
├─ .eslintrc                                          // eslint 配置文件（ts、js 代码格式检查）
├─ .gitignore                                         // git 忽略配置文件
├─ .prettierignore                                    // prettier 忽略配置文件
├─ .prettierrc                                        // prettier 配置文件
├─ .stylelintrc                                       // stylelint 配置文件（less 代码格式检查）
├─ jest.config.ts                                     // jest 配置文件（单元测试）
├─ package.json                                       // 项目配置 JSON
├─ README.md
├─ tsconfig.json                                      // ts 配置文件
└─ typings.d.ts                                       // ts 支持文件
```
